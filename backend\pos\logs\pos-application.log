2025-06-14 10:26:09.884 [main] INFO  com.mazai.pos.PosApplicationTests - Starting PosApplicationTests using Java 21.0.7 with PID 20860 (started by <PERSON> in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 10:26:09.890 [main] INFO  com.mazai.pos.PosApplicationTests - No active profile set, falling back to 1 default profile: "default"
2025-06-14 10:26:13.117 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 10:26:14.974 [main] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 10:26:15.169 [main] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 10:26:20.184 [main] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 10:26:20.186 [main] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 10:26:20.283 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 10:26:21.368 [main] INFO  com.mazai.pos.PosApplicationTests - Started PosApplicationTests in 12.105 seconds (process running for 13.783)
2025-06-14 10:26:22.206 [SpringApplicationShutdownHook] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 10:26:22.207 [SpringApplicationShutdownHook] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 12:39:36.279 [main] INFO  com.mazai.pos.PosApplicationTests - Starting PosApplicationTests using Java 21.0.7 with PID 21608 (started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 12:39:36.281 [main] INFO  com.mazai.pos.PosApplicationTests - No active profile set, falling back to 1 default profile: "default"
2025-06-14 12:39:39.517 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 12:39:41.410 [main] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 12:39:41.662 [main] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 12:39:46.681 [main] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 12:39:46.682 [main] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 12:39:46.780 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 12:39:47.977 [main] INFO  com.mazai.pos.PosApplicationTests - Started PosApplicationTests in 12.432 seconds (process running for 13.948)
2025-06-14 12:39:48.709 [SpringApplicationShutdownHook] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 12:39:48.710 [SpringApplicationShutdownHook] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 15:17:27.370 [main] INFO  com.mazai.pos.PosApplicationTests - Starting PosApplicationTests using Java 21.0.7 with PID 11872 (started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 15:17:27.386 [main] INFO  com.mazai.pos.PosApplicationTests - No active profile set, falling back to 1 default profile: "default"
2025-06-14 15:17:32.735 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 15:17:36.356 [main] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 15:17:36.658 [main] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 15:17:41.687 [main] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 15:17:41.689 [main] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 15:17:41.909 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 15:17:44.401 [main] INFO  com.mazai.pos.PosApplicationTests - Started PosApplicationTests in 18.003 seconds (process running for 20.493)
2025-06-14 15:17:45.723 [SpringApplicationShutdownHook] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 15:17:45.723 [SpringApplicationShutdownHook] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 21:50:23.189 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 21:50:23.191 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 21:50:25.775 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 21:50:27.512 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 21:50:27.694 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 21:50:32.710 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 21:50:32.711 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 21:50:32.815 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 21:50:33.512 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 10.975 seconds (process running for 11.589)
2025-06-14 21:50:43.706 [http-nio-8080-exec-1] INFO  c.m.pos.controller.AuthController - User admin logged in successfully
2025-06-14 21:51:46.194 [http-nio-8080-exec-2] INFO  c.m.pos.controller.ProductController - Creating new product: Cham
2025-06-14 21:51:46.237 [http-nio-8080-exec-2] INFO  c.m.pos.controller.ProductController - Product created successfully with ID: 51
2025-06-14 21:59:04.711 [Thread-5] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 21:59:04.711 [Thread-5] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 21:59:04.854 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 21:59:04.855 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 21:59:06.284 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 21:59:06.649 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 21:59:06.793 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 21:59:11.796 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 21:59:11.796 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 21:59:11.852 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 21:59:12.161 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 7.382 seconds (process running for 530.238)
2025-06-14 22:14:58.877 [http-nio-8080-exec-1] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:14:58.878 [http-nio-8080-exec-1] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749910498878}
2025-06-14 22:15:52.233 [http-nio-8080-exec-6] INFO  c.m.p.c.MobileOrderController - Creating order with 5 items
2025-06-14 22:15:52.306 [http-nio-8080-exec-6] INFO  c.m.p.c.MobileOrderController - Order #2 queued for kitchen printing
2025-06-14 22:15:52.307 [http-nio-8080-exec-6] INFO  c.m.p.c.MobileOrderController - Order created successfully with ID: 2
2025-06-14 22:15:52.309 [pool-3-thread-1] INFO  c.m.pos.service.MultiPrinterService - 使用printer3打印厨房订单 - 订单 #2, 包含2个商品 (类型: NEW)
2025-06-14 22:15:52.310 [pool-3-thread-1] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 开始打印厨房订单 #2 (类型: NEW)
2025-06-14 22:15:57.340 [pool-3-thread-1] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 发送打印命令失败 *************:9100 - Connect timed out
java.net.SocketTimeoutException: Connect timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:546)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at com.mazai.pos.service.impl.EscPosPrinterServiceImpl.sendToPrinterWithConfig(EscPosPrinterServiceImpl.java:726)
	at com.mazai.pos.service.impl.EscPosPrinterServiceImpl.printKitchenOrderWithPrinterAndType(EscPosPrinterServiceImpl.java:690)
	at com.mazai.pos.service.MultiPrinterService.printKitchenOrdersWithType(MultiPrinterService.java:120)
	at com.mazai.pos.service.impl.PrintQueueServiceImpl.lambda$0(PrintQueueServiceImpl.java:128)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-14 22:15:57.345 [pool-3-thread-1] ERROR c.m.pos.service.MultiPrinterService - printer3打印厨房订单失败
2025-06-14 22:15:57.345 [pool-3-thread-1] INFO  c.m.pos.service.MultiPrinterService - 使用printer1打印厨房订单 - 订单 #2, 包含3个商品 (类型: NEW)
2025-06-14 22:15:57.345 [pool-3-thread-1] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 开始打印厨房订单 #2 (类型: NEW)
2025-06-14 22:16:02.358 [pool-3-thread-1] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 发送打印命令失败 *************:9100 - Connect timed out
java.net.SocketTimeoutException: Connect timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:546)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at com.mazai.pos.service.impl.EscPosPrinterServiceImpl.sendToPrinterWithConfig(EscPosPrinterServiceImpl.java:726)
	at com.mazai.pos.service.impl.EscPosPrinterServiceImpl.printKitchenOrderWithPrinterAndType(EscPosPrinterServiceImpl.java:690)
	at com.mazai.pos.service.MultiPrinterService.printKitchenOrdersWithType(MultiPrinterService.java:120)
	at com.mazai.pos.service.impl.PrintQueueServiceImpl.lambda$0(PrintQueueServiceImpl.java:128)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-14 22:16:02.358 [pool-3-thread-1] ERROR c.m.pos.service.MultiPrinterService - printer1打印厨房订单失败
2025-06-14 22:17:31.016 [http-nio-8080-exec-1] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:17:31.017 [http-nio-8080-exec-1] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749910651017}
2025-06-14 22:17:46.792 [http-nio-8080-exec-3] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:17:46.792 [http-nio-8080-exec-3] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749910666792}
2025-06-14 22:18:59.953 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:18:59.953 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749910739953}
2025-06-14 22:19:15.083 [http-nio-8080-exec-2] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:19:15.084 [http-nio-8080-exec-2] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749910755084}
2025-06-14 22:20:07.383 [http-nio-8080-exec-4] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:20:07.383 [http-nio-8080-exec-4] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749910807383}
2025-06-14 22:32:27.668 [http-nio-8080-exec-9] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:32:27.668 [http-nio-8080-exec-9] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749911547668}
2025-06-14 22:32:48.397 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:32:48.397 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749911568397}
2025-06-14 22:33:02.236 [http-nio-8080-exec-5] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:33:02.237 [http-nio-8080-exec-5] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749911582237}
2025-06-14 22:35:38.666 [Thread-7] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:35:38.667 [Thread-7] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:35:38.944 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:35:38.945 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:35:40.287 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:35:40.788 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:35:41.022 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:35:46.029 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:35:46.029 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:35:46.097 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:35:46.444 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 7.682 seconds (process running for 2724.52)
2025-06-14 22:35:56.119 [Thread-11] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:35:56.119 [Thread-11] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:35:56.267 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:35:56.267 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:35:57.070 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:35:57.571 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:35:57.763 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:36:02.776 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:36:02.776 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:36:02.819 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:36:03.091 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.902 seconds (process running for 2741.167)
2025-06-14 22:36:15.892 [Thread-15] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:36:15.892 [Thread-15] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:36:15.986 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:36:15.987 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:36:16.598 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:36:16.881 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:36:17.023 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:36:22.035 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:36:22.035 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:36:22.079 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:36:22.339 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.396 seconds (process running for 2760.415)
2025-06-14 22:36:30.101 [Thread-19] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:36:30.101 [Thread-19] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:36:30.209 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:36:30.209 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:36:30.818 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:36:31.092 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:36:31.222 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:36:36.226 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:36:36.226 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:36:36.271 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:36:36.538 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.37 seconds (process running for 2774.615)
2025-06-14 22:36:41.273 [Thread-23] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:36:41.273 [Thread-23] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:36:41.411 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:36:41.412 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:36:42.075 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:36:42.367 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:36:42.521 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:36:47.533 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:36:47.533 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:36:47.587 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:36:47.886 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.531 seconds (process running for 2785.962)
2025-06-14 22:42:01.828 [Thread-27] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:42:01.828 [Thread-27] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:42:01.946 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:42:01.946 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:42:02.459 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:42:02.782 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:42:02.921 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:42:07.924 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:42:07.924 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:42:07.972 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:42:08.246 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.346 seconds (process running for 3106.323)
2025-06-14 22:42:23.051 [Thread-31] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:42:23.051 [Thread-31] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:42:23.126 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:42:23.126 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:42:23.774 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:42:24.093 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:42:24.224 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:42:29.237 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:42:29.237 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:42:29.278 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:42:29.524 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.426 seconds (process running for 3127.601)
2025-06-14 22:42:39.347 [Thread-35] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:42:39.348 [Thread-35] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:42:39.462 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:42:39.462 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:42:40.042 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:42:40.332 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:42:40.450 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:42:45.457 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:42:45.457 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:42:45.503 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:42:45.804 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.386 seconds (process running for 3143.881)
2025-06-14 22:43:24.274 [http-nio-8080-exec-4] INFO  c.m.pos.controller.ProductController - Creating new product: 星洲米粉
2025-06-14 22:43:24.282 [http-nio-8080-exec-4] INFO  c.m.pos.controller.ProductController - Product created successfully with ID: 52
2025-06-14 22:44:01.315 [http-nio-8080-exec-6] INFO  c.m.pos.controller.ProductController - Updating product with ID: 52
2025-06-14 22:44:01.327 [http-nio-8080-exec-6] INFO  c.m.pos.controller.ProductController - Product updated successfully: 星洲米粉
2025-06-14 22:45:46.285 [Thread-39] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:45:46.285 [Thread-39] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:45:46.389 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:45:46.390 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:45:46.770 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:45:47.044 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:45:47.170 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:45:52.178 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:45:52.178 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:45:52.219 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:45:52.478 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.128 seconds (process running for 3330.555)
2025-06-14 22:46:13.382 [Thread-43] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:46:13.382 [Thread-43] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:46:13.484 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:46:13.485 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:46:14.071 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:46:14.343 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:46:14.469 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:46:19.479 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:46:19.479 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:46:19.522 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:46:19.775 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.33 seconds (process running for 3357.852)
2025-06-14 22:46:23.519 [Thread-47] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:46:23.519 [Thread-47] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:46:23.616 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:46:23.616 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:46:24.093 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:46:24.357 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:46:24.487 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:46:29.498 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:46:29.498 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:46:29.542 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:46:29.804 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.226 seconds (process running for 3367.881)
2025-06-14 22:46:41.606 [Thread-51] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:46:41.606 [Thread-51] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:46:41.734 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:46:41.734 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:46:42.381 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:46:42.641 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:46:42.781 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:46:47.785 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:46:47.785 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:46:47.826 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:46:48.079 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.402 seconds (process running for 3386.156)
2025-06-14 22:48:11.515 [Thread-55] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:48:11.516 [Thread-55] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:48:11.595 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:48:11.595 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:48:12.076 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:48:12.326 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:48:12.435 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:48:17.439 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:48:17.439 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:48:17.480 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:48:17.733 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.168 seconds (process running for 3475.809)
2025-06-14 22:48:44.691 [Thread-59] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:48:44.691 [Thread-59] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:48:44.791 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:48:44.791 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:48:45.361 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:48:45.753 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:48:45.880 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:48:50.885 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:48:50.885 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:48:50.925 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:48:51.167 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.416 seconds (process running for 3509.243)
2025-06-14 22:49:03.016 [Thread-63] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:49:03.016 [Thread-63] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:49:03.122 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:49:03.122 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:49:03.648 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:49:03.981 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:49:04.106 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:49:09.116 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:49:09.116 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:49:09.163 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:49:09.426 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.341 seconds (process running for 3527.502)
2025-06-14 22:51:11.282 [Thread-67] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:51:11.282 [Thread-67] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:51:11.421 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:51:11.422 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:51:12.072 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:51:12.330 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:51:12.461 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:51:17.464 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:51:17.464 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:51:17.507 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:51:17.740 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.37 seconds (process running for 3655.816)
2025-06-14 22:51:31.596 [Thread-71] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-14 22:51:31.596 [Thread-71] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-14 22:51:31.678 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 19852 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-14 22:51:31.678 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-14 22:51:32.292 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 22:51:32.616 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-14 22:51:32.768 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-14 22:51:37.780 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-14 22:51:37.780 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-14 22:51:37.822 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 22:51:38.086 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.436 seconds (process running for 3676.163)
2025-06-14 22:56:39.960 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:56:39.961 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749912999961}

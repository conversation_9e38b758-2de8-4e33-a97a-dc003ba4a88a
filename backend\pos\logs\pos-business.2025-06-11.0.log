2025-06-11 18:32:58.648 [http-nio-8080-exec-2] WARN  c.m.pos.controller.AuthController - Bad credentials for user: admin
2025-06-11 18:33:03.464 [http-nio-8080-exec-3] INFO  c.m.pos.controller.AuthController - User admin logged in successfully
2025-06-11 18:33:11.028 [http-nio-8080-exec-3] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-11 18:33:11.028 [http-nio-8080-exec-3] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749637991028}
2025-06-11 18:33:46.079 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileOrderController - Creating order with 2 items
2025-06-11 18:33:46.169 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileOrderController - Order #161 queued for kitchen printing
2025-06-11 18:33:46.169 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileOrderController - Order created successfully with ID: 161
2025-06-11 18:34:14.361 [http-nio-8080-exec-10] INFO  c.m.p.c.MobilePaymentController - Order items payment order queued for printing (receiptType: CHINESE)
2025-06-11 19:06:31.831 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileOrderController - Creating order with 1 items
2025-06-11 19:06:31.870 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileOrderController - Order #162 queued for kitchen printing
2025-06-11 19:06:31.870 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileOrderController - Order created successfully with ID: 162
2025-06-11 19:15:10.298 [http-nio-8080-exec-5] INFO  c.m.p.c.MobileOrderController - Creating order with 1 items
2025-06-11 19:15:10.398 [http-nio-8080-exec-5] INFO  c.m.p.c.MobileOrderController - Order #163 queued for kitchen printing
2025-06-11 19:15:10.398 [http-nio-8080-exec-5] INFO  c.m.p.c.MobileOrderController - Order created successfully with ID: 163
2025-06-11 19:24:43.144 [http-nio-8080-exec-2] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-11 19:24:43.144 [http-nio-8080-exec-2] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749641083144}
2025-06-11 19:37:45.418 [http-nio-8080-exec-2] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-11 19:37:45.418 [http-nio-8080-exec-2] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749641865418}
2025-06-11 19:38:26.271 [http-nio-8080-exec-8] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-11 19:38:26.271 [http-nio-8080-exec-8] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749641906271}

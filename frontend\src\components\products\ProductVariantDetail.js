import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import ProductVariantService from '../../services/productVariantService';
import { useAuth } from '../../context/AuthContext';
import './ProductVariantDetail.css';

const ProductVariantDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const isAdmin = currentUser?.role === 'ADMIN';

  const [variant, setVariant] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchVariant();
  }, [id]);

  const fetchVariant = async () => {
    try {
      setLoading(true);
      const data = await ProductVariantService.getVariantById(id);
      setVariant(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch variant details. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this variant?')) {
      try {
        await ProductVariantService.deleteVariant(id);
        navigate('/product-variants');
      } catch (err) {
        setError('Failed to delete variant. Please try again.');
        console.error(err);
      }
    }
  };

  const getVariantTypeClass = (type) => {
    return type.toLowerCase();
  };

  if (loading) {
    return <div className="loading">Loading variant details...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  if (!variant) {
    return <div className="not-found">Variant not found.</div>;
  }

  return (
    <div className="variant-detail-container">
      <div className="variant-detail-header">
        <h2>Product Variant Details</h2>
        <div className="variant-actions">
          <Link to="/product-variants" className="btn-back">Back to Variants</Link>
          {isAdmin && (
            <>
              <Link to={`/product-variants/edit/${id}`} className="btn-edit">Edit</Link>
              <button onClick={handleDelete} className="btn-delete">Delete</button>
            </>
          )}
        </div>
      </div>

      <div className="variant-detail-card">
        <div className="variant-info">
          <h3>{variant.name}</h3>

          <div className="info-row">
            <div className="info-label">ID:</div>
            <div className="info-value">{variant.id}</div>
          </div>

          <div className="info-row">
            <div className="info-label">Product:</div>
            <div className="info-value">
              <Link to={`/products/${variant.product.id}`} className="product-link">
                {variant.product.name}
              </Link>
            </div>
          </div>

          <div className="info-row">
            <div className="info-label">Variant Type:</div>
            <div className="info-value">
              <span className={`variant-type ${getVariantTypeClass(variant.variantType)}`}>
                {variant.variantType}
              </span>
            </div>
          </div>

          <div className="info-row">
            <div className="info-label">Price Adjustment:</div>
            <div className="info-value">RM {variant.priceAdjustment.toFixed(2)}</div>
          </div>

          <div className="info-row">
            <div className="info-label">Default:</div>
            <div className="info-value">
              {variant.isDefault ? (
                <span className="default-badge">Default</span>
              ) : (
                'No'
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductVariantDetail;

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import OrderService from '../../services/orderService';
import './OrderDetail.css';

const OrderDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchOrder();
  }, [id]);

  const fetchOrder = async () => {
    try {
      setLoading(true);
      const data = await OrderService.getOrderById(id);
      setOrder(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch order details. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this order?')) {
      try {
        await OrderService.deleteOrder(id);
        navigate('/orders');
      } catch (err) {
        setError('Failed to delete order. Please try again.');
        console.error(err);
      }
    }
  };

  const getStatusClass = (status) => {
    switch (status) {
      case 'PENDING': return 'pending';
      case 'COMPLETED': return 'completed';
      case 'CANCELLED': return 'cancelled';
      default: return '';
    }
  };

  if (loading) {
    return <div className="loading">Loading order details...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  if (!order) {
    return <div className="not-found">Order not found.</div>;
  }

  return (
    <div className="order-detail-container">
      <div className="order-detail-header">
        <h2>Order Details</h2>
        <div className="order-actions">
          <Link to="/orders" className="btn-back">Back to Orders</Link>
          <Link to={`/orders/edit/${id}`} className="btn-edit">Edit</Link>
          <button onClick={handleDelete} className="btn-delete">Delete</button>
        </div>
      </div>

      <div className="order-detail-card">
        <div className="order-info">
          <div className="order-header">
            <h3>Order #{order.id}</h3>
            <span className={`status-badge ${getStatusClass(order.status)}`}>
              {order.status}
            </span>
          </div>

          <div className="info-section">
            <div className="info-row">
              <div className="info-label">Order Time:</div>
              <div className="info-value">
                {new Date(order.updatedAt || order.createdAt).toLocaleString()}
              </div>
            </div>

            <div className="info-row">
              <div className="info-label">Customer:</div>
              <div className="info-value">
                {order.user ? order.user.username : 'N/A'}
              </div>
            </div>

            <div className="info-row">
              <div className="info-label">Total Amount:</div>
              <div className="info-value total-amount">
                RM {order.totalAmount.toFixed(2)}
              </div>
            </div>

            <div className="info-row">
              <div className="info-label">Created:</div>
              <div className="info-value">
                {new Date(order.createdAt).toLocaleString()}
              </div>
            </div>

            <div className="info-row">
              <div className="info-label">Last Updated:</div>
              <div className="info-value">
                {new Date(order.updatedAt).toLocaleString()}
              </div>
            </div>
          </div>
        </div>

        <div className="order-items">
          <h4>Order Items</h4>

          {order.orderItems.length === 0 ? (
            <div className="no-items">No items in this order.</div>
          ) : (
            <div className="table-responsive">
              <table className="items-table">
                <thead>
                  <tr>
                    <th>Product</th>
                    <th>Price</th>
                    <th>Quantity</th>
                    <th>Subtotal</th>
                  </tr>
                </thead>
                <tbody>
                  {order.orderItems.map(item => (
                    <tr key={item.id}>
                      <td>{item.product.name}</td>
                      <td>RM {item.unitPrice.toFixed(2)}</td>
                      <td>{item.quantity}</td>
                      <td>RM {item.subtotal.toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan="3" className="total-label">Total:</td>
                    <td className="total-value">RM {order.totalAmount.toFixed(2)}</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderDetail;

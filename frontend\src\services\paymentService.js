import api from './api';

const PaymentService = {
  getAllPayments: async () => {
    try {
      const response = await api.get('/payments');
      return response.data;
    } catch (error) {
      console.error('Error fetching payments:', error);
      throw error;
    }
  },

  getPaymentById: async (id) => {
    try {
      const response = await api.get(`/payments/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching payment ${id}:`, error);
      throw error;
    }
  },

  getPaymentsByOrder: async (orderId) => {
    try {
      const response = await api.get(`/payments/order/${orderId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching payments for order ${orderId}:`, error);
      throw error;
    }
  },

  createPayment: async (paymentData) => {
    try {
      const response = await api.post('/payments', paymentData);
      return response.data;
    } catch (error) {
      console.error('Error creating payment:', error);
      throw error;
    }
  },

  processPayment: async (orderId, paymentMethod, issueReceipt) => {
    try {
      const response = await api.post('/payments/process', {
        orderId,
        paymentMethod,
        issueReceipt
      });
      return response.data;
    } catch (error) {
      console.error('Error processing payment:', error);
      throw error;
    }
  },

  deletePayment: async (id) => {
    try {
      await api.delete(`/payments/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting payment ${id}:`, error);
      throw error;
    }
  }
};

export default PaymentService;

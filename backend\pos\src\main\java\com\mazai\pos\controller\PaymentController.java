package com.mazai.pos.controller;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mazai.pos.model.Order;
import com.mazai.pos.model.Payment;
import com.mazai.pos.service.OrderService;
import com.mazai.pos.service.PaymentService;

@RestController
@RequestMapping("/api/payments")
public class PaymentController {

    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(PaymentController.class);

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private OrderService orderService;

    @GetMapping
    public ResponseEntity<List<Payment>> getAllPayments() {
        List<Payment> payments = paymentService.getAllPayments();
        return new ResponseEntity<>(payments, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Payment> getPaymentById(@PathVariable Long id) {
        Optional<Payment> payment = paymentService.getPaymentById(id);
        return payment.map(value -> new ResponseEntity<>(value, HttpStatus.OK))
                .orElseGet(() -> new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    @GetMapping("/order/{orderId}")
    public ResponseEntity<List<Payment>> getPaymentsByOrder(@PathVariable Long orderId) {
        Optional<Order> orderOpt = orderService.getOrderById(orderId);
        if (orderOpt.isPresent()) {
            List<Payment> payments = paymentService.getPaymentsByOrder(orderOpt.get());
            return new ResponseEntity<>(payments, HttpStatus.OK);
        }
        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @PostMapping
    public ResponseEntity<Payment> createPayment(@RequestBody Payment payment) {
        logger.info("Creating payment for order ID: {}, method: {}", payment.getOrder().getId(), payment.getPaymentMethod());
        Payment savedPayment = paymentService.createPayment(payment);
        logger.info("Payment created successfully for order: {}", savedPayment.getOrder().getId());
        return new ResponseEntity<>(savedPayment, HttpStatus.CREATED);
    }

    @PostMapping("/process")
    public ResponseEntity<Payment> processPayment(@RequestBody Map<String, Object> paymentRequest) {
        try {
            Long orderId = Long.valueOf(paymentRequest.get("orderId").toString());
            String paymentMethod = paymentRequest.get("paymentMethod").toString();
            boolean issueReceipt = Boolean.parseBoolean(paymentRequest.get("issueReceipt").toString());

            logger.info("Processing payment for order ID: {}, method: {}, receipt: {}", orderId, paymentMethod, issueReceipt);
            Payment payment = paymentService.processOrderPayment(orderId, paymentMethod, issueReceipt);
            logger.info("Payment processed successfully for order: {}", orderId);
            return new ResponseEntity<>(payment, HttpStatus.CREATED);
        } catch (Exception e) {
            logger.error("Error processing payment: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePayment(@PathVariable Long id) {
        paymentService.deletePayment(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}

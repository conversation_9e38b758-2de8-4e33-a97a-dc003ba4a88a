import axios from 'axios';

const API_URL = 'http://localhost:8080/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  withCredentials: true
});

// Add request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    try {
      console.log('API Request to:', config.url);
      console.log('Current location:', window.location.pathname);
      console.log('Request method:', config.method);

      // 检查当前请求是否是登录请求
      const isLoginRequest = config.url.includes('/auth/login');
      if (isLoginRequest) {
        console.log('This is a login request, not adding token');
        return config;
      }

      // 记录当前请求的详细信息，帮助调试
      console.log('Request details:', {
        url: config.url,
        method: config.method,
        headers: config.headers,
        data: config.data
      });

      // 确保 headers 对象存在
      if (!config.headers) {
        config.headers = {};
      }

      // 对于 OPTIONS 请求，不添加认证头
      if (config.method.toUpperCase() === 'OPTIONS') {
        console.log('This is an OPTIONS request, not adding token');
        return config;
      }

      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          console.log('User from localStorage:', {
            id: user.id,
            username: user.username,
            role: user.role,
            hasToken: !!user.token,
            hasAccessToken: !!user.accessToken
          });

          // 获取令牌
          const token = user.token || user.accessToken;

          if (token) {
            console.log('Adding token to request:', token.substring(0, 20) + '...');

            // 确保 headers 对象存在
            if (!config.headers) {
              config.headers = {};
            }

            // 设置认证头
            config.headers['Authorization'] = `Bearer ${token}`;
            console.log('Authorization header set:', config.headers['Authorization'].substring(0, 27) + '...');

            // 对于 multipart/form-data 请求，不设置 Content-Type
            if (config.headers['Content-Type'] && config.headers['Content-Type'].includes('multipart/form-data')) {
              console.log('This is a multipart/form-data request, not setting Content-Type');
              delete config.headers['Content-Type'];
            }
          } else {
            console.log('No token found in user object:', user);

            // 如果没有令牌但用户不在登录页面，重定向到登录页面
            if (window.location.pathname !== '/login') {
              console.log('No token but user is not on login page, redirecting to login');
              window.location.href = '/login';
              return config;
            }
          }
        } catch (parseError) {
          console.error('Error parsing user JSON:', parseError);
          console.log('Raw user string from localStorage:', userStr);

          // 清除可能损坏的用户数据
          localStorage.removeItem('user');

          // 如果用户不在登录页面，重定向到登录页面
          if (window.location.pathname !== '/login') {
            console.log('Invalid user data, redirecting to login');
            window.location.href = '/login';
            return config;
          }
        }
      } else {
        console.log('No user found in localStorage');

        // 如果没有用户数据但用户不在登录页面，重定向到登录页面
        if (window.location.pathname !== '/login') {
          console.log('No user data but user is not on login page, redirecting to login');
          window.location.href = '/login';
          return config;
        }
      }
    } catch (error) {
      console.error('Request interceptor error:', error);
    }

    // 最后记录完整的请求配置
    console.log('Final request config:', {
      url: config.url,
      method: config.method,
      headers: config.headers,
      data: config.data
    });

    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Add an interceptor to handle errors
api.interceptors.response.use(
  response => {
    console.log('API Response:', response.config.url, response.status);
    console.log('Response headers:', response.headers);
    return response;
  },
  error => {
    console.error('API Error:', error.config?.url, error.response?.status, error.message);

    if (error.response) {
      console.error('Error response data:', error.response.data);
      console.error('Error response headers:', error.response.headers);
      console.error('Error response status:', error.response.status);
      console.error('Error response status text:', error.response.statusText);
    } else if (error.request) {
      console.error('Error request:', error.request);
    } else {
      console.error('Error message:', error.message);
    }

    // Handle 401 Unauthorized errors
    if (error.response && error.response.status === 401) {
      console.error('Unauthorized error (401). Checking authentication state...');
      console.error('Request URL that caused 401:', error.config?.url);
      console.error('Request method:', error.config?.method);

      // 记录请求的详细信息
      if (error.config) {
        console.error('Full request config:', {
          url: error.config.url,
          method: error.config.method,
          headers: error.config.headers,
          data: error.config.data,
          baseURL: error.config.baseURL
        });
      }

      // Get current user from localStorage
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          console.error('User found in localStorage but request was unauthorized. Token may be invalid or expired.');
          console.error('User details:', {
            id: user.id,
            username: user.username,
            role: user.role,
            hasToken: !!user.token,
            tokenLength: user.token ? user.token.length : 0,
            tokenFirstChars: user.token ? user.token.substring(0, 20) + '...' : 'N/A'
          });

          // 检查请求头中是否包含了token
          if (error.config && error.config.headers) {
            console.error('Request headers:', error.config.headers);
            console.error('Authorization header in request:', error.config.headers.Authorization);
          }

          if (error.config && error.config.url && error.config.url.includes('product-variants')) {
            console.error('This is a product-variants request, attempting to retry...');

            // 不要立即清除用户数据和重定向，而是显示错误消息
            return Promise.reject(new Error('Authentication error. Please try refreshing the page.'));
          }

        } catch (e) {
          console.error('Error parsing user from localStorage:', e);
        }
      } else {
        console.error('No user found in localStorage. User is not logged in.');
      }

      // 如果不是product-variants相关的请求，或者重试失败，则清除用户数据并重定向
      localStorage.removeItem('user');
      console.error('User data cleared from localStorage');

      // Remove Authorization header
      delete api.defaults.headers.common['Authorization'];
      console.error('Authorization header removed from default headers');

      // 强制刷新页面，确保所有状态都被重置
      console.error('Force refreshing page to reset all states...');
      window.location.href = '/login';
    }

    // Handle 403 Forbidden errors
    if (error.response && error.response.status === 403) {
      console.error('Forbidden error (403). User does not have permission.');

      // 如果用户没有权限，可能是角色问题，但我们仍然保持登录状态
      console.error('User does not have permission to access this resource.');

      // 如果在受保护的路由上，可以选择重定向到首页或显示错误消息
      if (window.location.pathname !== '/login' && window.location.pathname !== '/') {
        console.error('Redirecting to home page...');
        window.location.href = '/';
      }
    }

    return Promise.reject(error);
  }
);

export default api;




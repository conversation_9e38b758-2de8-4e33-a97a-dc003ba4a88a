import mobileApi from './mobileApi';

const MobileProductService = {
  getAllProducts: async () => {
    try {
      console.log('MobileProductService: Fetching all products');
      const response = await mobileApi.get('/products');
      console.log('MobileProductService: Products fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('MobileProductService: Error fetching products:', error.response?.status, error.message);
      console.error('MobileProductService: Error details:', error.response?.data);
      throw error;
    }
  },

  getProductById: async (id) => {
    try {
      console.log(`MobileProductService: Fetching product with id ${id}`);
      const response = await mobileApi.get(`/products/${id}`);
      console.log(`MobileProductService: Product ${id} fetched successfully:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching product ${id}:`, error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      throw error;
    }
  },

  getProductsByCategory: async (category) => {
    try {
      const response = await mobileApi.get(`/products/category/${category}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching products by category ${category}:`, error);
      throw error;
    }
  },

  searchProducts: async (keyword) => {
    try {
      const response = await mobileApi.get(`/products/search?keyword=${keyword}`);
      return response.data;
    } catch (error) {
      console.error(`Error searching products with keyword ${keyword}:`, error);
      throw error;
    }
  }
};

export default MobileProductService;

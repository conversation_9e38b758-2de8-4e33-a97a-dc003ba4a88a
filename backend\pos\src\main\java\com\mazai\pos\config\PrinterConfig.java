package com.mazai.pos.config;

import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 打印机配置类
 * 从application.properties中读取打印机相关配置
 * 支持多台ESC/POS网络打印机，按产品类别分配打印任务
 */
@Configuration
@ConfigurationProperties(prefix = "printer")
public class PrinterConfig {

    // 打印机是否启用
    private boolean enabled = false;

    // 网络打印机配置（保持向后兼容）
    private Network network = new Network();

    // 多台打印机配置
    private Map<String, Network> printers = new HashMap<>();

    // 打印机类型：只支持ESCPOS
    private String type = "ESCPOS";

    // 打印机名称
    private String name = "ESC/POS Network Printer";

    // 字符编码
    private String encoding = "GBK";

    // 打印宽度（字符数）
    private int width = 32;

    // 是否自动切纸
    private boolean autoCut = true;



    // 网络打印机配置类
    public static class Network {
        // 打印机IP地址
        private String ip = "*************";

        // 打印机端口
        private int port = 9100;

        // 连接超时时间（毫秒）
        private int timeout = 5000;

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public int getTimeout() {
            return timeout;
        }

        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Network getNetwork() {
        return network;
    }

    public void setNetwork(Network network) {
        this.network = network;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEncoding() {
        return encoding;
    }

    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public boolean isAutoCut() {
        return autoCut;
    }

    public void setAutoCut(boolean autoCut) {
        this.autoCut = autoCut;
    }

    public Map<String, Network> getPrinters() {
        return printers;
    }

    public void setPrinters(Map<String, Network> printers) {
        this.printers = printers;
    }

    /**
     * 获取指定名称的打印机配置
     * @param printerName 打印机名称
     * @return 打印机网络配置，如果不存在则返回默认配置
     */
    public Network getPrinterConfig(String printerName) {
        return printers.getOrDefault(printerName, network);
    }

    /**
     * 检查是否配置了多台打印机
     * @return 是否有多台打印机配置
     */
    public boolean hasMultiplePrinters() {
        return !printers.isEmpty();
    }
}

package com.mazai.pos.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mazai.pos.config.PrinterConfig;
import com.mazai.pos.model.Order;
import com.mazai.pos.model.OrderItem;
import com.mazai.pos.service.impl.EscPosPrinterServiceImpl;

/**
 * 多打印机服务
 * 负责根据产品类别将打印任务分配到不同的打印机
 */
@Service
public class MultiPrinterService {

    private static final Logger logger = LoggerFactory.getLogger(MultiPrinterService.class);

    @Autowired
    private PrinterConfig printerConfig;

    @Autowired
    private EscPosPrinterServiceImpl escPosPrinterService;

    // 打印机分配规则
    private static final String PRINTER1 = "printer1"; // 收据 + 厨房订单（饮料、小食）
    private static final String PRINTER2 = "printer2"; // 厨房订单（煮炒）
    private static final String PRINTER3 = "printer3"; // 厨房订单（面食）

    // 产品类别常量
    private static final Set<String> PRINTER1_CATEGORIES = Set.of("饮料", "小食");
    private static final Set<String> PRINTER2_CATEGORIES = Set.of("煮炒");
    private static final Set<String> PRINTER3_CATEGORIES = Set.of("面食");

    /**
     * 打印收据到打印机1
     * @param order 订单
     * @return 是否打印成功
     */
    public boolean printReceipt(Order order) {
        return printReceiptWithType(order, "CHINESE");
    }

    /**
     * 打印收据到打印机1（支持多语言）
     * @param order 订单
     * @param receiptType 收据类型 (CHINESE/MALAY)
     * @return 是否打印成功
     */
    public boolean printReceiptWithType(Order order, String receiptType) {
        if (!printerConfig.isEnabled() || !printerConfig.hasMultiplePrinters()) {
            logger.info("多打印机功能未启用，使用默认打印机");
            return escPosPrinterService.printOrderWithReceiptType(order, receiptType);
        }

        try {
            logger.info("使用打印机1打印收据 - 订单 #{} (收据类型: {})", order.getId(), receiptType);
            return escPosPrinterService.printOrderWithPrinterAndReceiptType(order, PRINTER1, receiptType);
        } catch (Exception e) {
            logger.error("打印收据时出错: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据产品类别打印厨房订单到相应的打印机
     * @param order 订单
     * @return 打印结果映射（打印机名称 -> 是否成功）
     */
    public Map<String, Boolean> printKitchenOrders(Order order) {
        return printKitchenOrdersWithType(order, "NEW");
    }

    /**
     * 根据产品类别打印厨房订单到相应的打印机（支持订单类型）
     * @param order 订单
     * @param orderType 订单类型 (NEW/UPDATE)
     * @return 打印结果映射（打印机名称 -> 是否成功）
     */
    public Map<String, Boolean> printKitchenOrdersWithType(Order order, String orderType) {
        Map<String, Boolean> results = new HashMap<>();

        if (!printerConfig.isEnabled()) {
            logger.info("打印机功能未启用，跳过厨房订单打印");
            return results;
        }

        if (!printerConfig.hasMultiplePrinters()) {
            logger.info("多打印机功能未启用，使用默认打印机打印厨房订单");
            boolean result = escPosPrinterService.printKitchenOrderWithType(order, orderType);
            results.put("default", result);
            return results;
        }

        // 按类别分组订单项
        Map<String, List<OrderItem>> itemsByCategory = groupOrderItemsByCategory(order);

        // 为每个打印机创建包含相应类别商品的订单
        Map<String, Order> printerOrders = createPrinterOrders(order, itemsByCategory);

        // 打印到各个打印机
        for (Map.Entry<String, Order> entry : printerOrders.entrySet()) {
            String printerName = entry.getKey();
            Order printerOrder = entry.getValue();

            try {
                logger.info("使用{}打印厨房订单 - 订单 #{}, 包含{}个商品 (类型: {})",
                    printerName, order.getId(), printerOrder.getOrderItems().size(), orderType);

                boolean result = escPosPrinterService.printKitchenOrderWithPrinterAndType(printerOrder, printerName, orderType);
                results.put(printerName, result);

                if (result) {
                    logger.info("{}打印厨房订单成功", printerName);
                } else {
                    logger.error("{}打印厨房订单失败", printerName);
                }
            } catch (Exception e) {
                logger.error("使用{}打印厨房订单时出错: {}", printerName, e.getMessage(), e);
                results.put(printerName, false);
            }
        }

        return results;
    }

    /**
     * 按产品类别分组订单项
     * @param order 订单
     * @return 类别 -> 订单项列表的映射
     */
    private Map<String, List<OrderItem>> groupOrderItemsByCategory(Order order) {
        return order.getOrderItems().stream()
            .collect(Collectors.groupingBy(item -> {
                String category = item.getProduct().getCategory();
                return category != null ? category : "未分类";
            }));
    }

    /**
     * 为每个打印机创建包含相应类别商品的订单
     * @param originalOrder 原始订单
     * @param itemsByCategory 按类别分组的订单项
     * @return 打印机名称 -> 订单的映射
     */
    private Map<String, Order> createPrinterOrders(Order originalOrder, Map<String, List<OrderItem>> itemsByCategory) {
        Map<String, Order> printerOrders = new HashMap<>();

        // 为打印机1创建订单（饮料、小食）
        List<OrderItem> printer1Items = itemsByCategory.entrySet().stream()
            .filter(entry -> PRINTER1_CATEGORIES.contains(entry.getKey()))
            .flatMap(entry -> entry.getValue().stream())
            .collect(Collectors.toList());

        if (!printer1Items.isEmpty()) {
            Order printer1Order = createOrderCopy(originalOrder, printer1Items);
            printerOrders.put(PRINTER1, printer1Order);
        }

        // 为打印机2创建订单（煮炒 + 特殊处理"经济面"）
        List<OrderItem> printer2Items = new ArrayList<>();

        // 添加煮炒类别的商品
        printer2Items.addAll(itemsByCategory.entrySet().stream()
            .filter(entry -> PRINTER2_CATEGORIES.contains(entry.getKey()))
            .flatMap(entry -> entry.getValue().stream())
            .collect(Collectors.toList()));

        // 特殊处理"经济面"：只有状态为"下单"的才加入打印机2
        for (List<OrderItem> items : itemsByCategory.values()) {
            for (OrderItem item : items) {
                if ("经济面".equals(item.getProduct().getName()) && "下单".equals(item.getOrderStatus())) {
                    printer2Items.add(item);
                    logger.info("经济面商品状态为'下单'，加入打印机2 - 订单项ID: {}", item.getId());
                }
            }
        }

        if (!printer2Items.isEmpty()) {
            Order printer2Order = createOrderCopy(originalOrder, printer2Items);
            printerOrders.put(PRINTER2, printer2Order);
        }

        // 为打印机3创建订单（面食）
        List<OrderItem> printer3Items = itemsByCategory.entrySet().stream()
            .filter(entry -> PRINTER3_CATEGORIES.contains(entry.getKey()))
            .flatMap(entry -> entry.getValue().stream())
            .collect(Collectors.toList());

        if (!printer3Items.isEmpty()) {
            Order printer3Order = createOrderCopy(originalOrder, printer3Items);
            printerOrders.put(PRINTER3, printer3Order);
        }

        return printerOrders;
    }

    /**
     * 创建订单副本，只包含指定的订单项
     * @param originalOrder 原始订单
     * @param items 要包含的订单项
     * @return 订单副本
     */
    private Order createOrderCopy(Order originalOrder, List<OrderItem> items) {
        Order orderCopy = new Order();
        orderCopy.setId(originalOrder.getId());
        orderCopy.setTableNumber(originalOrder.getTableNumber());
        orderCopy.setStatus(originalOrder.getStatus());
        orderCopy.setCreatedAt(originalOrder.getCreatedAt());
        orderCopy.setUpdatedAt(originalOrder.getUpdatedAt()); // Add this line to copy the updatedAt field

        // 添加订单项
        for (OrderItem item : items) {
            orderCopy.addOrderItem(item);
        }

        return orderCopy;
    }

    /**
     * 获取打印机分配信息（用于调试）
     * @param order 订单
     * @return 打印机分配信息
     */
    public Map<String, List<String>> getPrinterAssignments(Order order) {
        Map<String, List<String>> assignments = new HashMap<>();
        Map<String, List<OrderItem>> itemsByCategory = groupOrderItemsByCategory(order);

        List<String> printer1Items = new ArrayList<>();
        List<String> printer2Items = new ArrayList<>();
        List<String> printer3Items = new ArrayList<>();

        for (Map.Entry<String, List<OrderItem>> entry : itemsByCategory.entrySet()) {
            String category = entry.getKey();
            List<String> itemNames = entry.getValue().stream()
                .map(item -> item.getProduct().getName())
                .collect(Collectors.toList());

            if (PRINTER1_CATEGORIES.contains(category)) {
                printer1Items.addAll(itemNames);
            } else if (PRINTER2_CATEGORIES.contains(category)) {
                printer2Items.addAll(itemNames);
            } else if (PRINTER3_CATEGORIES.contains(category)) {
                printer3Items.addAll(itemNames);
            }
        }

        assignments.put(PRINTER1, printer1Items);
        assignments.put(PRINTER2, printer2Items);
        assignments.put(PRINTER3, printer3Items);

        return assignments;
    }
}

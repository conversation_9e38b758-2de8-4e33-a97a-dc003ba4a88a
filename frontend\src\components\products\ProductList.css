.product-list-container {
  padding: 20px;
}

.product-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.btn-add {
  background-color: #4CAF50;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
}

.btn-add:hover {
  background-color: #45a049;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
}

.search-input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  width: 250px;
}

.category-select {
  padding: 10px;
  border: 1px solid #ddd;
  border-left: none;
  width: 150px;
  background-color: #f8f8f8;
}

.search-button {
  padding: 10px 15px;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

.search-button:hover {
  background-color: #0b7dda;
}

.table-responsive {
  overflow-x: auto;
}

.product-table {
  width: 100%;
  border-collapse: collapse;
}

.product-table th, .product-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}

.product-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.product-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.product-table tr:hover {
  background-color: #f1f1f1;
}

.actions {
  display: flex;
  gap: 5px;
}

.btn-view, .btn-edit, .btn-deactivate, .btn-activate {
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: white;
}

.btn-view {
  background-color: #2196F3;
}

.btn-edit {
  background-color: #FFC107;
}

.btn-deactivate {
  background-color: #F44336;
}

.btn-activate {
  background-color: #4CAF50;
}

.btn-view:hover {
  background-color: #0b7dda;
}

.btn-edit:hover {
  background-color: #e0a800;
}

.btn-deactivate:hover {
  background-color: #da190b;
}

.btn-activate:hover {
  background-color: #45a049;
}

.loading, .error, .no-products {
  padding: 20px;
  text-align: center;
}

.error {
  color: #F44336;
}

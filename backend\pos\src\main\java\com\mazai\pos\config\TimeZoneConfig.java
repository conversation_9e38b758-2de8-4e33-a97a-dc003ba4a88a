package com.mazai.pos.config;

import java.util.TimeZone;

import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

/**
 * 时区配置类
 * 确保整个应用程序使用马来西亚时区
 */
@Configuration
public class TimeZoneConfig {

    @PostConstruct
    public void init() {
        // 设置JVM默认时区为马来西亚时区
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kuala_Lumpur"));
        
        // 记录时区设置
        System.out.println("Application timezone set to: " + TimeZone.getDefault().getID());
        System.out.println("Current time zone offset: " + TimeZone.getDefault().getRawOffset() / (1000 * 60 * 60) + " hours");
    }
}

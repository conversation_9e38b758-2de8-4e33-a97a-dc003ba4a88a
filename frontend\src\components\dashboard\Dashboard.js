import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import './Dashboard.css';
import api from '../../services/api';
import { useAuth } from '../../context/AuthContext';

const Dashboard = () => {
  console.log('Dashboard component rendering');
  const navigate = useNavigate();
  const { isAuthenticated, currentUser } = useAuth();

  console.log('Dashboard isAuthenticated:', isAuthenticated);
  console.log('Dashboard currentUser:', currentUser ? {
    id: currentUser.id,
    username: currentUser.username,
    role: currentUser.role,
    hasToken: !!currentUser.token
  } : null);

  // 如果用户未认证，强制导航到登录页面
  useEffect(() => {
    console.log('Dashboard auth check effect running');
    if (!isAuthenticated) {
      console.log('Dashboard: User not authenticated, redirecting to login');
      navigate('/login');
    } else {
      console.log('Dashboard: User is authenticated, staying on dashboard');
    }
  }, [isAuthenticated, navigate]);
  const [stats, setStats] = useState({
    totalSales: 0,
    totalOrders: 0,
    totalProducts: 0,
    recentOrders: [],
    popularProducts: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        console.log('Dashboard: Fetching dashboard data...');

        // 检查认证状态
        const userStr = localStorage.getItem('user');
        if (!userStr) {
          console.error('Dashboard: No user found in localStorage');
          setError('You are not logged in. Please log in to view the dashboard.');
          setLoading(false);
          return;
        }

        try {
          const user = JSON.parse(userStr);
          console.log('Dashboard: User found in localStorage:', user.username);

          if (!user.token) {
            console.error('Dashboard: No token found in user object');
            setError('Authentication token missing. Please log in again.');
            setLoading(false);
            return;
          }

          console.log('Dashboard: Token available, proceeding with API calls');
        } catch (e) {
          console.error('Dashboard: Error parsing user from localStorage:', e);
          setError('Authentication error. Please log in again.');
          setLoading(false);
          return;
        }

        // 获取产品数据
        console.log('Dashboard: Fetching products...');
        let products = [];
        try {
          const productsResponse = await api.get('/products');
          products = productsResponse.data;
          console.log(`Dashboard: Successfully fetched ${products.length} products`);
        } catch (productError) {
          console.error('Dashboard: Error fetching products:', productError);
          if (productError.response) {
            console.error('Status:', productError.response.status);
            console.error('Data:', productError.response.data);
          }
          setError('Failed to load product data. Please try again later.');
          setLoading(false);
          return;
        }

        // 获取订单数据
        console.log('Dashboard: Fetching orders...');
        let orders = [];
        try {
          const ordersResponse = await api.get('/orders');
          orders = ordersResponse.data;
          console.log(`Dashboard: Successfully fetched ${orders.length} orders`);
        } catch (orderError) {
          console.error('Dashboard: Error fetching orders:', orderError);
          if (orderError.response) {
            console.error('Status:', orderError.response.status);
            console.error('Data:', orderError.response.data);
          }
          setError('Failed to load order data. Please try again later.');
          setLoading(false);
          return;
        }

        // 计算总销售额
        const totalSales = orders.reduce((sum, order) => sum + order.totalAmount, 0);
        console.log('Dashboard: Calculated total sales:', totalSales);

        // 获取最近订单（最后5个）
        const recentOrders = [...orders]
          .sort((a, b) => new Date(b.orderDate) - new Date(a.orderDate))
          .slice(0, 5);
        console.log('Dashboard: Processed recent orders:', recentOrders.length);

        // 模拟热门产品（在实际应用中，这将来自后端）
        // 这里我们只是将前5个产品作为占位符
        const popularProducts = products.slice(0, 5).map(product => ({
          ...product,
          soldCount: Math.floor(Math.random() * 100) // 模拟销售数量
        })).sort((a, b) => b.soldCount - a.soldCount);
        console.log('Dashboard: Processed popular products:', popularProducts.length);

        setStats({
          totalSales,
          totalOrders: orders.length,
          totalProducts: products.length,
          recentOrders,
          popularProducts
        });
        console.log('Dashboard: Dashboard data loaded successfully');

        setLoading(false);
      } catch (err) {
        console.error('Dashboard: Unexpected error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again later.');
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return <div className="dashboard-loading">Loading dashboard data...</div>;
  }

  if (error) {
    return <div className="dashboard-error">{error}</div>;
  }

  return (
    <div className="dashboard-container">
      <h1 className="dashboard-title">Mazai POS Dashboard</h1>

      <div className="stats-cards">
        <div className="stat-card sales">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <h3>Total Sales</h3>
            <p className="stat-value">RM {stats.totalSales.toFixed(2)}</p>
          </div>
        </div>

        <div className="stat-card orders">
          <div className="stat-icon">📋</div>
          <div className="stat-content">
            <h3>Total Orders</h3>
            <p className="stat-value">{stats.totalOrders}</p>
          </div>
        </div>

        <div className="stat-card products">
          <div className="stat-icon">🍽️</div>
          <div className="stat-content">
            <h3>Menu Items</h3>
            <p className="stat-value">{stats.totalProducts}</p>
          </div>
        </div>
      </div>

      <div className="dashboard-sections">
        <div className="dashboard-section">
          <div className="section-header">
            <h2>Recent Orders</h2>
            <Link to="/orders" className="view-all">View All</Link>
          </div>

          {stats.recentOrders.length === 0 ? (
            <p className="no-data">No recent orders</p>
          ) : (
            <div className="table-responsive">
              <table className="dashboard-table">
                <thead>
                  <tr>
                    <th>Order #</th>
                    <th>Date</th>
                    <th>Table Number</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {stats.recentOrders.map(order => (
                    <tr key={order.id}>
                      <td>{order.id}</td>
                      <td>{new Date(order.updatedAt || order.createdAt).toLocaleDateString()}</td>
                      <td>{order.tableNumber || 'N/A'}</td>
                      <td>RM {order.totalAmount.toFixed(2)}</td>
                      <td>
                        <span className={`status-badge ${order.status.toLowerCase()}`}>
                          {order.status}
                        </span>
                      </td>
                      <td>
                        <Link to={`/orders/${order.id}`} className="btn-view">View</Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        <div className="dashboard-section">
          <div className="section-header">
            <h2>Popular Menu Items</h2>
            <Link to="/products" className="view-all">View All</Link>
          </div>

          {stats.popularProducts.length === 0 ? (
            <p className="no-data">No products available</p>
          ) : (
            <div className="popular-products">
              {stats.popularProducts.map(product => (
                <div key={product.id} className="product-card">
                  <div className="product-info">
                    <h3>{product.name}</h3>
                    <p className="product-category">{product.category || 'Uncategorized'}</p>
                    <p className="product-price">RM {product.price.toFixed(2)}</p>
                  </div>
                  <div className="product-stats">
                    <span className="sold-count">{product.soldCount} sold</span>
                    <Link to={`/products/${product.id}`} className="btn-view">View</Link>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="quick-actions">
        <h2>Quick Actions</h2>
        <div className="action-buttons">
          <Link to="/orders/new" className="action-button new-order">
            <span className="action-icon">🛒</span>
            <span className="action-text">New Order</span>
          </Link>
          <Link to="/products/new" className="action-button new-product">
            <span className="action-icon">🍲</span>
            <span className="action-text">Add Menu Item</span>
          </Link>
          <Link to="/product-variants/new" className="action-button new-variant">
            <span className="action-icon">🔄</span>
            <span className="action-text">Add Variant</span>
          </Link>
          <Link to="/products" className="action-button manage-products">
            <span className="action-icon">📝</span>
            <span className="action-text">Manage Menu</span>
          </Link>
          <Link to="/orders" className="action-button view-orders">
            <span className="action-icon">📊</span>
            <span className="action-text">View Orders</span>
          </Link>
          <Link to="/payments" className="action-button view-payments">
            <span className="action-icon">💰</span>
            <span className="action-text">View Payments</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

import React, { useState, useEffect } from 'react';
import UserService from '../../services/userService';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import api from '../../services/api';
import './UserList.css';

const UserList = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { currentUser } = useAuth();
  const isAdmin = currentUser?.role === 'ADMIN';

  useEffect(() => {
    console.log('UserList: Component mounted, fetching users');
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      console.log('UserList: Fetching users');
      setLoading(true);
      const data = await UserService.getAllUsers();
      console.log('UserList: Users fetched successfully:', data);
      setUsers(data);
      setError(null);
    } catch (err) {
      console.error('UserList: Error fetching users:', err.message);
      console.error('UserList: Error details:', err.response?.data);
      setError('Failed to fetch users. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async (id) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        await UserService.deleteUser(id);
        setUsers(users.filter(user => user.id !== id));
      } catch (err) {
        setError('Failed to delete user. Please try again.');
        console.error(err);
      }
    }
  };

  const handleQuickPasswordReset = async (user) => {
    const newPassword = prompt(`Reset password for ${user.username}?\nEnter new password:`, 'temp123');

    if (newPassword && newPassword.trim()) {
      if (window.confirm(`Reset password for ${user.username} to "${newPassword}"?`)) {
        try {
          await api.post(`/admin/reset-password/${user.id}`, {
            newPassword: newPassword
          });
          alert(`Password reset successfully for ${user.username}`);
        } catch (error) {
          console.error('Error resetting password:', error);
          alert('Failed to reset password: ' + (error.response?.data?.message || error.message));
        }
      }
    }
  };

  if (loading) return <div className="loading">Loading users...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <div className="user-list-container">
      <div className="user-list-header">
        <h2>User Management</h2>
        {isAdmin && (
          <Link to="/users/new" className="btn-add">
            Add New User
          </Link>
        )}
      </div>

      {users.length === 0 ? (
        <p className="no-users">No users found. Add a new user to get started.</p>
      ) : (
        <table className="user-table">
          <thead>
            <tr>
              <th>ID</th>
              <th>Username</th>
              <th>Email</th>
              <th>Role</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {users.map(user => (
              <tr key={user.id}>
                <td>{user.id}</td>
                <td>{user.username}</td>
                <td>{user.email}</td>
                <td>{user.role}</td>
                <td>
                  <span className={`status ${user.active ? 'active' : 'inactive'}`}>
                    {user.active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="actions">
                  <Link to={`/users/${user.id}`} className="btn-view">
                    View
                  </Link>
                  {isAdmin && (
                    <>
                      <Link to={`/users/edit/${user.id}`} className="btn-edit">
                        Edit
                      </Link>
                      <button
                        className="btn-reset"
                        onClick={() => handleQuickPasswordReset(user)}
                      >
                        Reset Password
                      </button>
                      <button
                        className="btn-delete"
                        onClick={() => handleDeleteUser(user.id)}
                      >
                        Delete
                      </button>
                    </>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default UserList;
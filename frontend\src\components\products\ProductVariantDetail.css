.variant-detail-container {
  padding: 20px;
}

.variant-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.variant-actions {
  display: flex;
  gap: 10px;
}

.btn-back, .btn-edit, .btn-delete {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 14px;
}

.btn-back {
  background-color: #f2f2f2;
  color: #333;
}

.btn-edit {
  background-color: #FFC107;
  color: white;
}

.btn-delete {
  background-color: #F44336;
  color: white;
}

.btn-back:hover {
  background-color: #e0e0e0;
}

.btn-edit:hover {
  background-color: #e0a800;
}

.btn-delete:hover {
  background-color: #da190b;
}

.variant-detail-card {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.variant-info h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  font-size: 24px;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.info-label {
  font-weight: bold;
  width: 150px;
  color: #666;
}

.info-value {
  flex: 1;
}

.product-link {
  color: #2196F3;
  text-decoration: none;
}

.product-link:hover {
  text-decoration: underline;
}

.variant-type {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.variant-type.size {
  background-color: #2196F3;
}

.variant-type.addon {
  background-color: #9C27B0;
}

.variant-type.packaging {
  background-color: #FF9800;
}

.variant-type.noodle {
  background-color: #8BC34A;
}

.variant-type.tea {
  background-color: #795548;
}

.default-badge {
  display: inline-block;
  background-color: #4CAF50;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.loading, .error, .not-found {
  padding: 20px;
  text-align: center;
}

.error {
  color: #F44336;
}

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import OrderService from '../../services/orderService';
import ProductService from '../../services/productService';
import UserService from '../../services/userService';
import './OrderForm.css';

const OrderForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditMode = !!id;

  const [formData, setFormData] = useState({
    status: 'OPEN', // 更新为新的状态枚举值
    tableNumber: '',
    orderItems: []
  });

  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});

  // For adding new items
  const [newItem, setNewItem] = useState({
    productId: '',
    quantity: 1
  });

  useEffect(() => {
    fetchProducts();

    if (isEditMode) {
      fetchOrder();
    }
  }, [id]);

  // 移除fetchUsers函数

  const fetchProducts = async () => {
    try {
      const data = await ProductService.getAllProducts();
      setProducts(data);
    } catch (err) {
      console.error('Failed to fetch products:', err);
    }
  };

  const fetchOrder = async () => {
    try {
      setLoading(true);
      const order = await OrderService.getOrderById(id);

      setFormData({
        status: order.status,
        tableNumber: order.tableNumber || '',
        orderItems: order.orderItems.map(item => ({
          id: item.id,
          productId: item.product.id,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          subtotal: item.subtotal,
          remark: item.remark || '',
          product: item.product
        }))
      });
    } catch (err) {
      setError('Failed to fetch order details. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear validation error when field is edited
    if (validationErrors[name]) {
      setValidationErrors({
        ...validationErrors,
        [name]: null
      });
    }
  };

  const handleNewItemChange = (e) => {
    const { name, value } = e.target;
    setNewItem({
      ...newItem,
      [name]: value
    });
  };

  const addOrderItem = () => {
    if (!newItem.productId || newItem.quantity < 1) {
      setError('Please select a product and enter a valid quantity.');
      return;
    }

    const product = products.find(p => p.id === parseInt(newItem.productId));
    if (!product) {
      setError('Selected product not found.');
      return;
    }

    const quantity = parseInt(newItem.quantity);
    const unitPrice = product.price;
    const subtotal = unitPrice * quantity;

    const newOrderItem = {
      productId: parseInt(newItem.productId),
      quantity: quantity,
      unitPrice: unitPrice,
      subtotal: subtotal,
      remark: '',
      product: product
    };

    setFormData({
      ...formData,
      orderItems: [...formData.orderItems, newOrderItem]
    });

    // Reset new item form
    setNewItem({
      productId: '',
      quantity: 1
    });

    setError(null);
  };

  const removeOrderItem = (index) => {
    const updatedItems = [...formData.orderItems];
    updatedItems.splice(index, 1);

    setFormData({
      ...formData,
      orderItems: updatedItems
    });
  };

  const calculateTotal = () => {
    return formData.orderItems.reduce((total, item) => total + item.subtotal, 0).toFixed(2);
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.tableNumber) {
      errors.tableNumber = 'Please enter a table number';
    }

    if (formData.orderItems.length === 0) {
      errors.orderItems = 'Please add at least one item to the order';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      // Prepare order data
      const orderData = {
        status: formData.status,
        totalAmount: parseFloat(calculateTotal()),
        tableNumber: parseInt(formData.tableNumber) || null,
        orderItems: formData.orderItems.map(item => ({
          id: item.id, // Include ID for existing items in edit mode
          product: { id: item.productId },
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          subtotal: item.subtotal,
          variantIds: JSON.stringify([]), // 添加空的变体IDs JSON字符串
          remark: item.remark || '' // 添加备注字段
        }))
      };

      if (isEditMode) {
        orderData.id = parseInt(id);
        await OrderService.updateOrder(id, orderData);
      } else {
        await OrderService.createOrder(orderData);
      }

      navigate('/orders');
    } catch (err) {
      setError(`Failed to ${isEditMode ? 'update' : 'create'} order. ${err.response?.data || 'Please try again.'}`);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  if (loading && isEditMode) {
    return <div className="loading">Loading order details...</div>;
  }

  return (
    <div className="order-form-container">
      <h2>{isEditMode ? 'Edit Order' : 'Create New Order'}</h2>

      {error && <div className="error-message">{error}</div>}

      <form onSubmit={handleSubmit} className="order-form">
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="status">Status*</label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleChange}
            >
              <option value="OPEN">Open</option>
              <option value="CLOSED">Closed</option>
            </select>
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="tableNumber">Table Number*</label>
          <input
            type="number"
            id="tableNumber"
            name="tableNumber"
            value={formData.tableNumber}
            onChange={handleChange}
            className={validationErrors.tableNumber ? 'error' : ''}
            min="1"
          />
          {validationErrors.tableNumber && <div className="error-text">{validationErrors.tableNumber}</div>}
        </div>

        <div className="order-items-section">
          <h3>Order Items</h3>
          {validationErrors.orderItems && <div className="error-text">{validationErrors.orderItems}</div>}

          <div className="add-item-form">
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="productId">Product</label>
                <select
                  id="productId"
                  name="productId"
                  value={newItem.productId}
                  onChange={handleNewItemChange}
                >
                  <option value="">Select Product</option>
                  {products.map(product => (
                    <option key={product.id} value={product.id}>
                      {product.name} - ${product.price.toFixed(2)}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="quantity">Quantity</label>
                <input
                  type="number"
                  id="quantity"
                  name="quantity"
                  min="1"
                  value={newItem.quantity}
                  onChange={handleNewItemChange}
                />
              </div>

              <button
                type="button"
                onClick={addOrderItem}
                className="btn-add-item"
              >
                Add Item
              </button>
            </div>
          </div>

          {formData.orderItems.length > 0 ? (
            <div className="table-responsive">
              <table className="items-table">
                <thead>
                  <tr>
                    <th>Product</th>
                    <th>Price</th>
                    <th>Quantity</th>
                    <th>Subtotal</th>
                    <th>Remark</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {formData.orderItems.map((item, index) => (
                    <tr key={index}>
                      <td>{item.product.name}</td>
                      <td>${item.unitPrice.toFixed(2)}</td>
                      <td>{item.quantity}</td>
                      <td>${item.subtotal.toFixed(2)}</td>
                      <td>
                        <input
                          type="text"
                          value={item.remark || ''}
                          onChange={(e) => {
                            const updatedItems = [...formData.orderItems];
                            updatedItems[index].remark = e.target.value;
                            setFormData({
                              ...formData,
                              orderItems: updatedItems
                            });
                          }}
                          placeholder="Add remark"
                          className="remark-input"
                        />
                      </td>
                      <td>
                        <button
                          type="button"
                          onClick={() => removeOrderItem(index)}
                          className="btn-remove-item"
                        >
                          Remove
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan="3" className="total-label">Total:</td>
                    <td colSpan="3" className="total-value">${calculateTotal()}</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          ) : (
            <div className="no-items">No items added to this order yet.</div>
          )}
        </div>

        <div className="form-actions">
          <button
            type="button"
            onClick={() => navigate('/orders')}
            className="btn-cancel"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn-save"
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Order'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default OrderForm;

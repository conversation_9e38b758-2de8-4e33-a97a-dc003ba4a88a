import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import OrderService from '../../services/orderService';
import { useAuth } from '../../context/AuthContext';
import './OrderList.css';

// 日期格式化函数
const formatDate = (dateValue) => {
  try {
    // 如果是字符串，尝试转换为日期对象
    if (typeof dateValue === 'string') {
      // 尝试直接创建日期对象
      const date = new Date(dateValue);
      if (!isNaN(date.getTime())) {
        return date.toLocaleDateString();
      }

      // 如果是ISO格式但缺少时区信息，添加Z
      if (dateValue.includes('T') && !dateValue.includes('Z')) {
        const dateWithZ = dateValue + 'Z';
        const date = new Date(dateWithZ);
        if (!isNaN(date.getTime())) {
          return date.toLocaleDateString();
        }
      }

      // 如果是简单日期格式 (2023-01-01)
      if (dateValue.length === 10 && dateValue.includes('-')) {
        const date = new Date(dateValue + 'T00:00:00Z');
        if (!isNaN(date.getTime())) {
          return date.toLocaleDateString();
        }
      }
    }
    // 如果是数字（时间戳），直接创建日期对象
    else if (typeof dateValue === 'number') {
      const date = new Date(dateValue);
      if (!isNaN(date.getTime())) {
        return date.toLocaleDateString();
      }
    }
    // 如果是日期对象，直接使用
    else if (dateValue instanceof Date) {
      if (!isNaN(dateValue.getTime())) {
        return dateValue.toLocaleDateString();
      }
    }

    // 如果所有尝试都失败，返回一个默认值
    console.warn('Failed to format date:', dateValue);
    return '日期不可用';
  } catch (error) {
    console.error('Error formatting date:', error, dateValue);
    return '日期格式错误';
  }
};

const OrderList = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [statusFilter, setStatusFilter] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const isAdmin = currentUser?.role === 'ADMIN';

  useEffect(() => {
    console.log('OrderList: Component mounted, fetching orders');
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      console.log('OrderList: Fetching orders');
      setLoading(true);
      const data = await OrderService.getAllOrders();
      console.log('OrderList: Orders fetched successfully:', data);

      // 检查返回的数据是否是数组
      if (Array.isArray(data)) {
        setOrders(data);
        setError(null);
      } else {
        console.error('OrderList: Expected array but got:', typeof data, data);
        setOrders([]);
        setError('Invalid data format received from server. Please try again later.');
      }
    } catch (err) {
      console.error('OrderList: Error fetching orders:', err.message);
      console.error('OrderList: Error details:', err.response?.data);
      setError('Failed to fetch orders. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this order?')) {
      try {
        await OrderService.deleteOrder(id);
        setOrders(orders.filter(order => order.id !== id));
      } catch (err) {
        setError('Failed to delete order. Please try again.');
        console.error(err);
      }
    }
  };

  const handleStatusFilter = async () => {
    if (!statusFilter) {
      fetchOrders();
      return;
    }

    try {
      setLoading(true);
      const data = await OrderService.getOrdersByStatus(statusFilter);

      // 检查返回的数据是否是数组
      if (Array.isArray(data)) {
        setOrders(data);
        setError(null);
      } else {
        console.error('OrderList: Expected array but got:', typeof data, data);
        setOrders([]);
        setError('Invalid data format received from server. Please try again later.');
      }
    } catch (err) {
      setError('Failed to filter orders. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDateFilter = async () => {
    if (!startDate || !endDate) {
      setError('Please select both start and end dates.');
      return;
    }

    try {
      setLoading(true);
      const data = await OrderService.getOrdersByDateRange(startDate, endDate);

      // 检查返回的数据是否是数组
      if (Array.isArray(data)) {
        setOrders(data);
        setError(null);
      } else {
        console.error('OrderList: Expected array but got:', typeof data, data);
        setOrders([]);
        setError('Invalid data format received from server. Please try again later.');
      }
    } catch (err) {
      setError('Failed to filter orders by date. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const resetFilters = () => {
    setStatusFilter('');
    setStartDate('');
    setEndDate('');
    fetchOrders();
  };

  if (loading) {
    return <div className="loading">Loading orders...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  return (
    <div className="order-list-container">
      <div className="order-list-header">
        <h2>Orders</h2>
        {isAdmin && <Link to="/orders/new" className="btn-add">Create New Order</Link>}
      </div>

      <div className="filters-container">
        <div className="filter-group">
          <label>Status:</label>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="">All Statuses</option>
            <option value="OPEN">Open</option>
            <option value="CLOSED">Closed</option>
          </select>
          <button onClick={handleStatusFilter} className="filter-button">Filter</button>
        </div>

        <div className="filter-group">
          <label>Date Range:</label>
          <input
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
          />
          <span>to</span>
          <input
            type="date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
          />
          <button onClick={handleDateFilter} className="filter-button">Filter</button>
        </div>

        <button onClick={resetFilters} className="reset-button">Reset Filters</button>
      </div>

      {orders.length === 0 ? (
        <div className="no-orders">No orders found.</div>
      ) : (
        <div className="table-responsive">
          <table className="order-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Date</th>
                <th>Table Number</th>
                <th>Total Amount</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {orders.map(order => (
                <tr key={order.id}>
                  <td>{order.id}</td>
                  <td>{formatDate(order.updatedAt || order.createdAt)}</td>
                  <td>{order.tableNumber || 'N/A'}</td>
                  <td>RM {order.totalAmount.toFixed(2)}</td>
                  <td>
                    <span className={`status-badge ${order.status.toLowerCase()}`}>
                      {order.status}
                    </span>
                  </td>
                  <td className="actions">
                    <button
                      onClick={() => navigate(`/orders/${order.id}`)}
                      className="btn-view"
                    >
                      View
                    </button>
                    {isAdmin && (
                      <>
                        <button
                          onClick={() => navigate(`/orders/edit/${order.id}`)}
                          className="btn-edit"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDelete(order.id)}
                          className="btn-delete"
                        >
                          Delete
                        </button>
                      </>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default OrderList;

.product-form-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.product-form-container h2 {
  margin-bottom: 20px;
}

.product-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input, .form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.form-group input.error, .form-group textarea.error {
  border-color: #F44336;
}

.error-text {
  color: #F44336;
  font-size: 14px;
  margin-top: 5px;
}

.error-message {
  background-color: #FFEBEE;
  color: #F44336;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.image-preview {
  margin-top: 10px;
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 200px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.btn-cancel, .btn-save {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.btn-cancel {
  background-color: #f2f2f2;
  color: #333;
}

.btn-save {
  background-color: #4CAF50;
  color: white;
}

.btn-cancel:hover {
  background-color: #e0e0e0;
}

.btn-save:hover {
  background-color: #45a049;
}

.btn-save:disabled {
  background-color: #a5d6a7;
  cursor: not-allowed;
}

.loading {
  text-align: center;
  padding: 20px;
}

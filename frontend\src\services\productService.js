import api from './api';

const ProductService = {
  getAllProducts: async () => {
    try {
      console.log('ProductService: Fetching all products');
      const response = await api.get('/products');
      console.log('ProductService: Products fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('ProductService: Error fetching products:', error.response?.status, error.message);
      console.error('ProductService: Error details:', error.response?.data);
      throw error;
    }
  },

  getProductById: async (id) => {
    try {
      console.log(`ProductService: Fetching product with id ${id}`);
      const response = await api.get(`/products/${id}`);
      console.log(`ProductService: Product ${id} fetched successfully:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching product ${id}:`, error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      throw error;
    }
  },

  getProductsByCategory: async (category) => {
    try {
      const response = await api.get(`/products/category/${category}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching products by category ${category}:`, error);
      throw error;
    }
  },

  searchProducts: async (keyword) => {
    try {
      const response = await api.get(`/products/search?keyword=${keyword}`);
      return response.data;
    } catch (error) {
      console.error(`Error searching products with keyword ${keyword}:`, error);
      throw error;
    }
  },

  createProduct: async (productData) => {
    try {
      const response = await api.post('/products', productData);
      return response.data;
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  },

  updateProduct: async (id, productData) => {
    try {
      console.log('ProductService: Updating product with id', id);
      console.log('ProductService: Request data:', productData);

      const response = await api.put(`/products/${id}`, productData);
      console.log('ProductService: Update successful, response:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error updating product ${id}:`, error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      throw error;
    }
  },

  createProductWithImage: async (productData, imageFile) => {
    try {
      const formData = new FormData();
      formData.append('product', new Blob([JSON.stringify(productData)], { type: 'application/json' }));

      if (imageFile) {
        formData.append('image', imageFile);
      }

      const response = await api.post('/products/with-image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error creating product with image:', error);
      throw error;
    }
  },

  updateProductWithImage: async (id, productData, imageFile) => {
    try {
      const formData = new FormData();
      formData.append('product', new Blob([JSON.stringify(productData)], { type: 'application/json' }));

      if (imageFile) {
        formData.append('image', imageFile);
      }

      const response = await api.put(`/products/${id}/with-image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      console.error(`Error updating product ${id} with image:`, error);
      throw error;
    }
  },

  uploadProductImage: async (id, imageFile) => {
    try {
      const formData = new FormData();
      formData.append('image', imageFile);

      const response = await api.post(`/products/${id}/upload-image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      console.error(`Error uploading image for product ${id}:`, error);
      throw error;
    }
  },

  deleteProduct: async (id) => {
    try {
      console.log(`ProductService: Deleting product with id ${id}`);
      const response = await api.delete(`/products/${id}`);
      console.log(`ProductService: Product ${id} deleted successfully`);
      return true;
    } catch (error) {
      console.error(`Error deleting product ${id}:`, error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      throw error;
    }
  },

  toggleProductAvailability: async (id, currentStatus) => {
    try {
      console.log(`ProductService: Toggling availability for product ${id}`);
      // 首先获取产品信息
      const product = await ProductService.getProductById(id);

      // 更新可用性状态
      product.isAvailable = !currentStatus;

      // 保存更新后的产品
      const response = await ProductService.updateProduct(id, product);
      console.log(`ProductService: Product ${id} availability toggled to ${!currentStatus}`);
      return response;
    } catch (error) {
      console.error(`Error toggling availability for product ${id}:`, error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      throw error;
    }
  }
};

export default ProductService;



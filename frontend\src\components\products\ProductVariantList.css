.product-list-container {
  padding: 20px;
}

.product-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.product-list-header h2 {
  margin: 0;
}

.btn-add {
  background-color: #4CAF50;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 14px;
}

.btn-add:hover {
  background-color: #45a049;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
}

.search-input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  width: 250px;
}

.category-select {
  padding: 10px;
  border: 1px solid #ddd;
  border-left: none;
  width: 150px;
  background-color: #f8f8f8;
}

.search-button {
  padding: 10px 15px;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

.search-button:hover {
  background-color: #0b7dda;
}

.loading, .error, .no-products {
  padding: 20px;
  text-align: center;
}

.error {
  color: #F44336;
}

.table-responsive {
  overflow-x: auto;
}

.product-table {
  width: 100%;
  border-collapse: collapse;
}

.product-table th, .product-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}

.product-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.product-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.product-table tr:hover {
  background-color: #f1f1f1;
}

.actions {
  display: flex;
  gap: 5px;
}

.btn-view, .btn-edit, .btn-delete {
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: white;
}

.btn-view {
  background-color: #2196F3;
}

.btn-edit {
  background-color: #FFC107;
}

.btn-delete {
  background-color: #F44336;
}

.btn-view:hover {
  background-color: #0b7dda;
}

.btn-edit:hover {
  background-color: #e0a800;
}

.btn-delete:hover {
  background-color: #da190b;
}

.variant-type {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.variant-type.size {
  background-color: #2196F3;
  color: white;
}

.variant-type.addon {
  background-color: #9C27B0;
  color: white;
}

.variant-type.packaging {
  background-color: #FF9800;
  color: white;
}

.variant-type.noodle {
  background-color: #8BC34A;
  color: white;
}

.variant-type.tea {
  background-color: #795548;
  color: white;
}

.default-badge {
  display: inline-block;
  padding: 2px 6px;
  background-color: #4CAF50;
  color: white;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 5px;
}

.filter-container {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.filter-select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

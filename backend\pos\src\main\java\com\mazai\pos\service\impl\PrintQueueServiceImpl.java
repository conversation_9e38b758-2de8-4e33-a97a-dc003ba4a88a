package com.mazai.pos.service.impl;

import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mazai.pos.model.Order;
import com.mazai.pos.model.PrintTask;
import com.mazai.pos.service.MultiPrinterService;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

/**
 * 打印队列服务
 * 用于异步处理打印任务，避免打印操作阻塞主线程
 */
@Service
public class PrintQueueServiceImpl {

    @Autowired
    private MultiPrinterService multiPrinterService;

    private final BlockingQueue<PrintTask> printQueue = new LinkedBlockingQueue<>();
    private ExecutorService executorService;
    private boolean running = false;

    // 统计信息
    private final AtomicInteger totalProcessed = new AtomicInteger(0);
    private final AtomicInteger totalSuccessful = new AtomicInteger(0);
    private final AtomicInteger totalFailed = new AtomicInteger(0);

    /**
     * 初始化打印队列服务
     */
    @PostConstruct
    public void init() {
        executorService = Executors.newSingleThreadExecutor();
        startProcessing();
    }

    /**
     * 将订单加入打印队列（收据格式）
     * @param order 要打印的订单
     */
    public void queueOrderForPrinting(Order order) {
        queueOrderForPrintingWithReceiptType(order, "CHINESE");
    }

    /**
     * 将订单加入打印队列（收据格式，支持多语言）
     * @param order 要打印的订单
     * @param receiptType 收据类型 (CHINESE/MALAY)
     */
    public void queueOrderForPrintingWithReceiptType(Order order, String receiptType) {
        if (order == null) {
            return;
        }
        try {
            PrintTask task = new PrintTask(order, PrintTask.PrintType.RECEIPT, receiptType);
            printQueue.offer(task);
            System.out.println("订单 #" + order.getId() + " 已加入收据打印队列 (收据类型: " + receiptType + ")");
        } catch (Exception e) {
            System.err.println("将订单加入打印队列时出错: " + e.getMessage());
        }
    }

    /**
     * 将订单加入厨房打印队列
     * @param order 要打印的订单
     */
    public void queueKitchenOrderForPrinting(Order order) {
        queueKitchenOrderForPrintingWithType(order, "NEW");
    }

    /**
     * 将订单加入厨房打印队列（支持订单类型）
     * @param order 要打印的订单
     * @param orderType 订单类型 (NEW/UPDATE)
     */
    public void queueKitchenOrderForPrintingWithType(Order order, String orderType) {
        if (order == null) {
            return;
        }
        try {
            PrintTask task = new PrintTask(order, PrintTask.PrintType.KITCHEN_ORDER, orderType);
            printQueue.offer(task);
            System.out.println("订单 #" + order.getId() + " 已加入厨房打印队列 (类型: " + orderType + ")");
        } catch (Exception e) {
            System.err.println("将厨房订单加入打印队列时出错: " + e.getMessage());
        }
    }

    /**
     * 启动打印队列处理线程
     */
    private void startProcessing() {
        running = true;
        executorService.submit(() -> {
            System.out.println("打印队列处理线程已启动");

            while (running) {
                try {
                    // 从队列中获取打印任务，最多等待1秒
                    PrintTask printTask = printQueue.poll(1, TimeUnit.SECONDS);

                    if (printTask != null) {
                        totalProcessed.incrementAndGet();
                        Order order = printTask.getOrder();

                        try {
                            boolean printResult = false;
                            if (printTask.getPrintType() == PrintTask.PrintType.RECEIPT) {
                                // 使用多打印机服务打印收据到打印机1，支持多语言
                                String receiptType = printTask.getReceiptType();
                                printResult = multiPrinterService.printReceiptWithType(order, receiptType);
                                System.out.println("处理收据打印任务 - 订单 #" + order.getId() + " (收据类型: " + receiptType + ")");
                            } else if (printTask.getPrintType() == PrintTask.PrintType.KITCHEN_ORDER) {
                                // 使用多打印机服务按类别分配厨房订单到不同打印机，支持订单类型
                                String orderType = printTask.getReceiptType(); // 复用 receiptType 字段存储订单类型
                                Map<String, Boolean> kitchenResults = multiPrinterService.printKitchenOrdersWithType(order, orderType);
                                System.out.println("处理厨房订单打印任务 - 订单 #" + order.getId() + " (类型: " + orderType + ")");

                                // 检查是否至少有一个打印机成功打印
                                printResult = kitchenResults.values().stream().anyMatch(result -> result);

                                // 输出详细的打印结果
                                for (Map.Entry<String, Boolean> entry : kitchenResults.entrySet()) {
                                    String printerName = entry.getKey();
                                    Boolean success = entry.getValue();
                                    if (success) {
                                        System.out.println("  " + printerName + " 打印成功");
                                    } else {
                                        System.err.println("  " + printerName + " 打印失败");
                                    }
                                }
                            }

                            if (printResult) {
                                totalSuccessful.incrementAndGet();
                                System.out.println("订单 #" + order.getId() + " " +
                                    (printTask.getPrintType() == PrintTask.PrintType.RECEIPT ? "收据" : "厨房订单") +
                                    " 已成功打印 (成功: " + totalSuccessful.get() + "/" + totalProcessed.get() + ")");
                            } else {
                                totalFailed.incrementAndGet();
                                System.err.println("打印订单 #" + order.getId() + " " +
                                    (printTask.getPrintType() == PrintTask.PrintType.RECEIPT ? "收据" : "厨房订单") +
                                    " 失败 (失败: " + totalFailed.get() + "/" + totalProcessed.get() + ")");
                            }
                        } catch (Exception e) {
                            totalFailed.incrementAndGet();
                            System.err.println("打印订单 #" + order.getId() + " " +
                                (printTask.getPrintType() == PrintTask.PrintType.RECEIPT ? "收据" : "厨房订单") +
                                " 时出错: " + e.getMessage() + " (失败: " + totalFailed.get() + "/" + totalProcessed.get() + ")");
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    running = false;
                    System.out.println("打印队列处理线程已中断");
                }
            }
        });
    }

    /**
     * 关闭打印队列服务
     */
    @PreDestroy
    public void shutdown() {
        running = false;
        executorService.shutdown();

        try {
            // 等待最多5秒钟让任务完成
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
            System.out.println("打印队列服务已关闭");
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
            System.err.println("关闭打印队列服务时出错: " + e.getMessage());
        }
    }

    /**
     * 获取当前队列中的订单数量
     * @return 队列中的订单数量
     */
    public int getQueueSize() {
        return printQueue.size();
    }

    /**
     * 清空打印队列
     */
    public void clearQueue() {
        printQueue.clear();
        System.out.println("打印队列已清空");
    }

    /**
     * 获取打印统计信息
     * @return 包含统计信息的字符串
     */
    public String getPrintStatistics() {
        int processed = totalProcessed.get();
        int successful = totalSuccessful.get();
        int failed = totalFailed.get();
        double successRate = processed > 0 ? (double) successful / processed * 100 : 0;

        return String.format("打印统计 - 总计: %d, 成功: %d, 失败: %d, 成功率: %.1f%%",
            processed, successful, failed, successRate);
    }

    /**
     * 获取成功打印数量
     */
    public int getSuccessfulPrints() {
        return totalSuccessful.get();
    }

    /**
     * 获取失败打印数量
     */
    public int getFailedPrints() {
        return totalFailed.get();
    }

    /**
     * 获取总处理数量
     */
    public int getTotalProcessed() {
        return totalProcessed.get();
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        totalProcessed.set(0);
        totalSuccessful.set(0);
        totalFailed.set(0);
        System.out.println("打印统计信息已重置");
    }
}

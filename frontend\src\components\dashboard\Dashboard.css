.dashboard-container {
  padding: 20px;
}

.dashboard-title {
  margin-bottom: 25px;
  color: #333;
  font-size: 28px;
  border-bottom: 2px solid #f0ad4e;
  padding-bottom: 10px;
}

.dashboard-loading, .dashboard-error {
  text-align: center;
  padding: 40px;
  font-size: 18px;
}

.dashboard-error {
  color: #d9534f;
}

/* Stats Cards */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  align-items: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.sales {
  border-left: 5px solid #5cb85c;
}

.stat-card.orders {
  border-left: 5px solid #5bc0de;
}

.stat-card.products {
  border-left: 5px solid #f0ad4e;
}

.stat-icon {
  font-size: 2.5rem;
  margin-right: 15px;
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: #666;
}

.stat-value {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

/* Dashboard Sections */
.dashboard-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.dashboard-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.section-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.view-all {
  color: #5bc0de;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

.view-all:hover {
  text-decoration: underline;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #777;
  font-style: italic;
}

/* Tables */
.table-responsive {
  overflow-x: auto;
}

.dashboard-table {
  width: 100%;
  border-collapse: collapse;
}

.dashboard-table th, .dashboard-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.dashboard-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #555;
}

.dashboard-table tr:hover {
  background-color: #f5f5f5;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  color: white;
  display: inline-block;
}

.status-badge.pending, .status-badge.open {
  background-color: #f0ad4e;
}

.status-badge.completed, .status-badge.closed {
  background-color: #5cb85c;
}

.status-badge.cancelled {
  background-color: #d9534f;
}

/* Additional status styles */
.status-badge.paid {
  background-color: #5cb85c;
}

.status-badge.served {
  background-color: #5bc0de;
}

.btn-view {
  background-color: #5bc0de;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  text-decoration: none;
  font-size: 12px;
  display: inline-block;
}

.btn-view:hover {
  background-color: #46b8da;
}

/* Popular Products */
.popular-products {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.product-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 15px;
  border: 1px solid #eee;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.product-info h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-category {
  color: #777;
  font-size: 12px;
  margin: 0 0 8px 0;
}

.product-price {
  font-weight: bold;
  color: #5cb85c;
  margin: 0 0 10px 0;
  font-size: 16px;
}

.product-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.sold-count {
  font-size: 12px;
  color: #777;
}

/* Quick Actions */
.quick-actions {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.quick-actions h2 {
  margin: 0 0 15px 0;
  font-size: 20px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  text-decoration: none;
  color: #333;
  transition: all 0.3s ease;
  border: 1px solid #eee;
}

.action-button.new-order {
  background-color: #f0f7ff;
  border-color: #cce5ff;
}

.action-button.new-product {
  background-color: #fff7e6;
  border-color: #ffeeba;
}

.action-button.new-variant {
  background-color: #f3e5f5;
  border-color: #e1bee7;
}

.action-button.manage-products {
  background-color: #e6f7ff;
  border-color: #b8daff;
}

.action-button.view-orders {
  background-color: #f0fff0;
  border-color: #c3e6cb;
}

.action-button.view-payments {
  background-color: #e8f5e9;
  border-color: #a5d6a7;
}

.action-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.action-text {
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-sections {
    grid-template-columns: 1fr;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .popular-products {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .action-buttons {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 480px) {
  .action-buttons {
    grid-template-columns: 1fr;
  }
}

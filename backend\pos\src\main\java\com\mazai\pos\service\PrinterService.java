package com.mazai.pos.service;

import com.mazai.pos.model.Order;

/**
 * 打印服务接口
 * 定义打印功能的基本方法
 */
public interface PrinterService {

    /**
     * 连接打印机
     * @return 是否连接成功
     */
    boolean connect();

    /**
     * 断开与打印机的连接
     */
    void disconnect();

    /**
     * 检查打印机连接状态
     * @return 是否已连接
     */
    boolean isConnected();

    /**
     * 打印订单
     * @param order 要打印的订单
     * @return 是否打印成功
     */
    boolean printOrder(Order order);

    /**
     * 获取打印机名称
     * @return 打印机名称
     */
    String getPrinterName();

    /**
     * 打印测试页
     * @return 是否打印成功
     */
    boolean printTestPage();

    /**
     * 打印简单测试
     * @param content 可选的自定义内容
     * @return 是否打印成功
     */
    boolean printSimpleTest(String content);

    /**
     * 打印收据测试
     * @return 是否打印成功
     */
    boolean printReceiptTest();
}

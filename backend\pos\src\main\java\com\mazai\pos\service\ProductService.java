package com.mazai.pos.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mazai.pos.model.Product;
import com.mazai.pos.repository.ProductRepository;

@Service
public class ProductService {

    @Autowired
    private ProductRepository productRepository;

    public List<Product> getAllProducts() {
        return productRepository.findAll();
    }

    public Optional<Product> getProductById(Long id) {
        return productRepository.findById(id);
    }

    public List<Product> getProductsByCategory(String category) {
        return productRepository.findByCategory(category);
    }

    public List<Product> searchProducts(String keyword) {
        return productRepository.findByNameContaining(keyword);
    }



    public Product createProduct(Product product) {
        return productRepository.save(product);
    }

    public Product updateProduct(Product product) {
        // Get the existing product to preserve created_at
        Optional<Product> existingProductOpt = productRepository.findById(product.getId());
        if (existingProductOpt.isPresent()) {
            Product existingProduct = existingProductOpt.get();
            
            // Preserve the created_at timestamp
            product.setCreatedAt(existingProduct.getCreatedAt());
            
            // Update the updated_at timestamp 
            product.setUpdatedAt(LocalDateTime.now());
        }
        
        return productRepository.save(product);
    }

    public void deleteProduct(Long id) {
        productRepository.deleteById(id);
    }

    public boolean updateStock(Long productId, int quantity) {
        // Stock functionality has been removed
        // Always return true to indicate success without updating stock
        return true;
    }
}



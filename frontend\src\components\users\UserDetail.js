import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import UserService from '../../services/userService';
import './UserDetail.css';

const UserDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    fetchUser();
  }, [id]);
  
  const fetchUser = async () => {
    try {
      setLoading(true);
      const userData = await UserService.getUserById(id);
      setUser(userData);
    } catch (err) {
      setError('Failed to fetch user details. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  
  const handleDeleteUser = async () => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        await UserService.deleteUser(id);
        navigate('/users');
      } catch (err) {
        setError('Failed to delete user. Please try again.');
        console.error(err);
      }
    }
  };
  
  if (loading) return <div className="loading">Loading user details...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!user) return <div className="error">User not found</div>;
  
  return (
    <div className="user-detail-container">
      <div className="user-detail-header">
        <h2>User Details</h2>
        <div className="user-actions">
          <Link to="/users" className="btn-back">
            Back to List
          </Link>
          <Link to={`/users/edit/${id}`} className="btn-edit">
            Edit
          </Link>
          <button className="btn-delete" onClick={handleDeleteUser}>
            Delete
          </button>
        </div>
      </div>
      
      <div className="user-info">
        <div className="info-group">
          <div className="info-label">ID:</div>
          <div className="info-value">{user.id}</div>
        </div>
        
        <div className="info-group">
          <div className="info-label">Username:</div>
          <div className="info-value">{user.username}</div>
        </div>
        
        <div className="info-group">
          <div className="info-label">Email:</div>
          <div className="info-value">{user.email}</div>
        </div>
        
        <div className="info-group">
          <div className="info-label">Role:</div>
          <div className="info-value">{user.role}</div>
        </div>
        
        <div className="info-group">
          <div className="info-label">Status:</div>
          <div className="info-value">
            <span className={`status-badge ${user.active ? 'active' : 'inactive'}`}>
              {user.active ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>
        
        <div className="info-group">
          <div className="info-label">Created At:</div>
          <div className="info-value">
            {user.createdAt ? new Date(user.createdAt).toLocaleString() : 'N/A'}
          </div>
        </div>
        
        <div className="info-group">
          <div className="info-label">Updated At:</div>
          <div className="info-value">
            {user.updatedAt ? new Date(user.updatedAt).toLocaleString() : 'N/A'}
          </div>
        </div>
      </div>
      
      {/* Additional sections can be added here, such as user activity, orders, etc. */}
      <div className="user-detail-section">
        <h3>Recent Activity</h3>
        <div className="empty-state">No recent activity found for this user.</div>
      </div>
    </div>
  );
};

export default UserDetail;

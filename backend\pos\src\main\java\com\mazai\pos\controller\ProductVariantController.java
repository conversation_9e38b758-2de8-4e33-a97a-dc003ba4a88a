package com.mazai.pos.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mazai.pos.model.Product;
import com.mazai.pos.model.ProductVariant;
import com.mazai.pos.service.ProductService;
import com.mazai.pos.service.ProductVariantService;

@RestController
@RequestMapping("/api/product-variants")
public class ProductVariantController {

    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(ProductVariantController.class);

    @Autowired
    private ProductVariantService productVariantService;

    @Autowired
    private ProductService productService;

    @GetMapping
    public ResponseEntity<List<ProductVariant>> getAllVariants() {
        try {
            System.out.println("ProductVariantController: getAllVariants() called");
            List<ProductVariant> variants = productVariantService.getAllVariants();
            System.out.println("ProductVariantController: Successfully retrieved " + variants.size() + " variants");
            return new ResponseEntity<>(variants, HttpStatus.OK);
        } catch (Exception e) {
            System.err.println("ProductVariantController: Error retrieving variants: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ProductVariant> getVariantById(@PathVariable Long id) {
        Optional<ProductVariant> variant = productVariantService.getVariantById(id);
        return variant.map(value -> new ResponseEntity<>(value, HttpStatus.OK))
                .orElseGet(() -> new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    @GetMapping("/product/{productId}")
    public ResponseEntity<List<ProductVariant>> getVariantsByProduct(@PathVariable Long productId) {
        Optional<Product> product = productService.getProductById(productId);
        if (product.isPresent()) {
            List<ProductVariant> variants = productVariantService.getVariantsByProduct(product.get());
            return new ResponseEntity<>(variants, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @PostMapping
    public ResponseEntity<ProductVariant> createVariant(@RequestBody ProductVariant variant) {
        try {
            ProductVariant newVariant = productVariantService.createVariant(variant);
            return new ResponseEntity<>(newVariant, HttpStatus.CREATED);
        } catch (Exception e) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<ProductVariant> updateVariant(@PathVariable Long id, @RequestBody ProductVariant variant) {
        Optional<ProductVariant> existingVariant = productVariantService.getVariantById(id);
        if (existingVariant.isPresent()) {
            variant.setId(id);
            ProductVariant updatedVariant = productVariantService.updateVariant(variant);
            return new ResponseEntity<>(updatedVariant, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteVariant(@PathVariable Long id) {
        Optional<ProductVariant> existingVariant = productVariantService.getVariantById(id);
        if (existingVariant.isPresent()) {
            productVariantService.deleteVariant(id);
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }
}

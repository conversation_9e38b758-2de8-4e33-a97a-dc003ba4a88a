.variant-form-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.variant-form-container h2 {
  margin-bottom: 20px;
}

.variant-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input, .form-group select, .form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.form-group input.error, .form-group select.error {
  border-color: #F44336;
}

.error-text {
  color: #F44336;
  font-size: 14px;
  margin-top: 5px;
}

.error-message {
  background-color: #FFEBEE;
  color: #F44336;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin-right: 10px;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 30px;
}

.btn-cancel, .btn-save {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.btn-cancel {
  background-color: #f2f2f2;
  color: #333;
}

.btn-save {
  background-color: #4CAF50;
  color: white;
}

.btn-cancel:hover {
  background-color: #e0e0e0;
}

.btn-save:hover {
  background-color: #45a049;
}

.btn-save:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.loading {
  text-align: center;
  padding: 20px;
}

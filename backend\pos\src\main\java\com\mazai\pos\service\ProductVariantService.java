package com.mazai.pos.service;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mazai.pos.model.Product;
import com.mazai.pos.model.ProductVariant;
import com.mazai.pos.repository.ProductVariantRepository;

@Service
public class ProductVariantService {

    @Autowired
    private ProductVariantRepository productVariantRepository;

    public List<ProductVariant> getAllVariants() {
        try {
            System.out.println("ProductVariantService: getAllVariants() called");
            List<ProductVariant> variants = productVariantRepository.findAll();
            System.out.println("ProductVariantService: Successfully retrieved " + variants.size() + " variants");
            return variants;
        } catch (Exception e) {
            System.err.println("ProductVariantService: Error retrieving variants: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    public Optional<ProductVariant> getVariantById(Long id) {
        return productVariantRepository.findById(id);
    }

    public List<ProductVariant> getVariantsByIds(List<Long> ids) {
        return productVariantRepository.findAllById(ids);
    }

    public List<ProductVariant> getVariantsByProduct(Product product) {
        return productVariantRepository.findByProduct(product);
    }

    public List<ProductVariant> getVariantsByProductAndType(Product product, String variantType) {
        return productVariantRepository.findByProductAndVariantType(product, variantType);
    }

    public List<ProductVariant> getDefaultVariantsByProduct(Product product) {
        return productVariantRepository.findByProductAndIsDefaultTrue(product);
    }

    public ProductVariant createVariant(ProductVariant variant) {
        // 如果这是默认变体，确保同一产品的同一类型没有其他默认变体
        if (variant.getIsDefault()) {
            handleDefaultVariant(variant);
        }
        return productVariantRepository.save(variant);
    }

    public ProductVariant updateVariant(ProductVariant variant) {
        // 如果这是默认变体，确保同一产品的同一类型没有其他默认变体
        if (variant.getIsDefault()) {
            handleDefaultVariant(variant);
        }
        return productVariantRepository.save(variant);
    }

    public void deleteVariant(Long id) {
        productVariantRepository.deleteById(id);
    }

    // 处理默认变体逻辑
    private void handleDefaultVariant(ProductVariant newDefaultVariant) {
        // 获取同一产品同一类型的所有变体
        List<ProductVariant> existingVariants = productVariantRepository.findByProductAndVariantType(
                newDefaultVariant.getProduct(), newDefaultVariant.getVariantType());

        // 将所有其他变体设置为非默认
        for (ProductVariant existingVariant : existingVariants) {
            // 跳过当前正在更新的变体
            if (newDefaultVariant.getId() != null && existingVariant.getId().equals(newDefaultVariant.getId())) {
                continue;
            }

            if (existingVariant.getIsDefault()) {
                existingVariant.setIsDefault(false);
                productVariantRepository.save(existingVariant);
            }
        }
    }
}

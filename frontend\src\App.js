import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link, Navigate, useLocation } from 'react-router-dom';
import './App.css';

// User components
import UserList from './components/users/UserList';
import UserForm from './components/users/UserForm';
import UserDetail from './components/users/UserDetail';

// Product components
import ProductList from './components/products/ProductList';
import ProductForm from './components/products/ProductForm';
import ProductDetail from './components/products/ProductDetail';
import ProductVariantList from './components/products/ProductVariantList';
import ProductVariantForm from './components/products/ProductVariantForm';
import ProductVariantDetail from './components/products/ProductVariantDetail';

// Order components
import OrderList from './components/orders/OrderList';
import OrderForm from './components/orders/OrderForm';
import OrderDetail from './components/orders/OrderDetail';

// Payment components
import PaymentList from './components/payments/PaymentList';
import PaymentDetail from './components/payments/PaymentDetail';

// Dashboard component
import Dashboard from './components/dashboard/Dashboard';

// Diagnostic components (removed PrinterTest)

// Auth components
import StandaloneLoginPage from './components/auth/StandaloneLoginPage';
import ProtectedRoute from './components/common/ProtectedRoute';

import { AuthProvider, useAuth } from './context/AuthContext';

function AppHeader() {
  const { currentUser, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <header className="App-header">
      <h1>Mazai POS System</h1>
      <nav className="main-nav">
        <ul>
          <li><Link to="/">Dashboard</Link></li>
          <li><Link to="/users">Staff</Link></li>
          <li><Link to="/products">Menu</Link></li>
          <li><Link to="/product-variants">Variants</Link></li>
          <li><Link to="/orders">Orders</Link></li>
          <li><Link to="/payments">Payments</Link></li>

          {currentUser && (
            <li className="user-menu">
              <span>{currentUser.username} ({currentUser.role})</span>
              <button onClick={handleLogout} className="logout-btn">Logout</button>
            </li>
          )}
        </ul>
      </nav>
    </header>
  );
}

function AppContent() {
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const isLoginPage = location.pathname === '/login';

  console.log('AppContent rendering, current path:', location.pathname);
  console.log('AppContent isAuthenticated:', isAuthenticated);
  console.log('AppContent isLoginPage:', isLoginPage);

  return (
    <main className={isLoginPage ? "" : "App-content"}>
      <Routes>
        <Route path="/login" element={<StandaloneLoginPage />} />

        {/* 主页路由 */}
        <Route path="/" element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } />

        {/* User Routes */}
        <Route path="/users" element={
          <ProtectedRoute>
            <UserList />
          </ProtectedRoute>
        } />
        <Route path="/users/new" element={
          <ProtectedRoute>
            <UserForm />
          </ProtectedRoute>
        } />
        <Route path="/users/edit/:id" element={
          <ProtectedRoute>
            <UserForm />
          </ProtectedRoute>
        } />
        <Route path="/users/:id" element={
          <ProtectedRoute>
            <UserDetail />
          </ProtectedRoute>
        } />

        {/* Product Routes */}
        <Route path="/products" element={
          <ProtectedRoute>
            <ProductList />
          </ProtectedRoute>
        } />
        <Route path="/products/new" element={
          <ProtectedRoute>
            <ProductForm />
          </ProtectedRoute>
        } />
        <Route path="/products/edit/:id" element={
          <ProtectedRoute>
            <ProductForm />
          </ProtectedRoute>
        } />
        <Route path="/products/:id" element={
          <ProtectedRoute>
            <ProductDetail />
          </ProtectedRoute>
        } />

        {/* Order Routes */}
        <Route path="/orders" element={
          <ProtectedRoute>
            <OrderList />
          </ProtectedRoute>
        } />
        <Route path="/orders/new" element={
          <ProtectedRoute>
            <OrderForm />
          </ProtectedRoute>
        } />
        <Route path="/orders/edit/:id" element={
          <ProtectedRoute>
            <OrderForm />
          </ProtectedRoute>
        } />
        <Route path="/orders/:id" element={
          <ProtectedRoute>
            <OrderDetail />
          </ProtectedRoute>
        } />

        {/* Product Variant Routes */}
        <Route path="/product-variants" element={
          <ProtectedRoute>
            <ProductVariantList />
          </ProtectedRoute>
        } />
        <Route path="/product-variants/new" element={
          <ProtectedRoute>
            <ProductVariantForm />
          </ProtectedRoute>
        } />
        <Route path="/product-variants/edit/:id" element={
          <ProtectedRoute>
            <ProductVariantForm />
          </ProtectedRoute>
        } />
        <Route path="/product-variants/:id" element={
          <ProtectedRoute>
            <ProductVariantDetail />
          </ProtectedRoute>
        } />

        {/* Payment Routes */}
        <Route path="/payments" element={
          <ProtectedRoute>
            <PaymentList />
          </ProtectedRoute>
        } />
        <Route path="/payments/:id" element={
          <ProtectedRoute>
            <PaymentDetail />
          </ProtectedRoute>
        } />

        {/* Diagnostic Routes - removed printer test */}





        {/* Redirect to login if no route matches */}
        <Route path="*" element={<Navigate to="/login" />} />
      </Routes>
    </main>
  );
}

function AppLayout() {
  const location = useLocation();
  const { isAuthenticated, currentUser } = useAuth();
  const isLoginPage = location.pathname === '/login';

  console.log('AppLayout rendering, current path:', location.pathname);
  console.log('AppLayout isAuthenticated:', isAuthenticated);
  console.log('AppLayout isLoginPage:', isLoginPage);
  console.log('AppLayout currentUser:', currentUser ? {
    id: currentUser.id,
    username: currentUser.username,
    role: currentUser.role,
    hasToken: !!currentUser.token
  } : null);

  return (
    <div className="App">
      {!isLoginPage && <AppHeader />}
      <AppContent />
      {!isLoginPage && (
        <footer className="App-footer">
          <p>&copy; {new Date().getFullYear()} Mazai POS System. All rights reserved.</p>
        </footer>
      )}
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <Router>
        <AppLayout />
      </Router>
    </AuthProvider>
  );
}

export default App;


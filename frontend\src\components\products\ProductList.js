import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import ProductService from '../../services/productService';
import { useAuth } from '../../context/AuthContext';
import './ProductList.css';

const ProductList = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [categories, setCategories] = useState([]);
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const isAdmin = currentUser?.role === 'ADMIN';

  useEffect(() => {
    console.log('ProductList: Component mounted, fetching products');
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      console.log('ProductList: Fetching products');
      setLoading(true);
      const data = await ProductService.getAllProducts();
      console.log('ProductList: Products fetched successfully:', data);
      setProducts(data);

      // 提取所有唯一的类别
      const uniqueCategories = [...new Set(data.map(product => product.category).filter(Boolean))];
      setCategories(uniqueCategories);

      setError(null);
    } catch (err) {
      console.error('ProductList: Error fetching products:', err.message);
      console.error('ProductList: Error details:', err.response?.data);
      setError('Failed to fetch products. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleAvailability = async (id, currentStatus) => {
    const action = currentStatus ? 'deactivate' : 'activate';
    if (window.confirm(`Are you sure you want to ${action} this product?`)) {
      try {
        const updatedProduct = await ProductService.toggleProductAvailability(id, currentStatus);
        // 更新产品列表中的产品状态
        setProducts(products.map(product =>
          product.id === id ? updatedProduct : product
        ));
      } catch (err) {
        setError(`Failed to ${action} product. Please try again.`);
        console.error(err);
      }
    }
  };

  const handleSearch = async () => {
    try {
      setLoading(true);

      // 如果没有搜索词和类别，获取所有产品
      if (!searchTerm.trim() && !selectedCategory) {
        fetchProducts();
        return;
      }

      // 如果有类别但没有搜索词
      if (selectedCategory && !searchTerm.trim()) {
        const data = await ProductService.getProductsByCategory(selectedCategory);
        setProducts(data);
      }
      // 如果有搜索词但没有类别
      else if (searchTerm.trim() && !selectedCategory) {
        const data = await ProductService.searchProducts(searchTerm);
        setProducts(data);
      }
      // 如果同时有搜索词和类别，先按名称搜索，然后在前端过滤类别
      else {
        const data = await ProductService.searchProducts(searchTerm);
        const filteredData = data.filter(product => product.category === selectedCategory);
        setProducts(filteredData);
      }

      setError(null);
    } catch (err) {
      setError('Failed to search products. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  if (loading) {
    return <div className="loading">Loading products...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  return (
    <div className="product-list-container">
      <div className="product-list-header">
        <h2>Menu Items</h2>
        {isAdmin && <Link to="/products/new" className="btn-add">Add New Menu Item</Link>}
      </div>

      <div className="search-container">
        <input
          type="text"
          placeholder="Search by name..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onKeyPress={handleKeyPress}
          className="search-input"
        />
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="category-select"
        >
          <option value="">All Categories</option>
          {categories.map(category => (
            <option key={category} value={category}>{category}</option>
          ))}
        </select>
        <button onClick={handleSearch} className="search-button">Search</button>
      </div>

      {products.length === 0 ? (
        <div className="no-products">No products found.</div>
      ) : (
        <div className="table-responsive">
          <table className="product-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Item Name</th>
                <th>Alt Name</th>
                <th>Price</th>
                <th>Category</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {products.map(product => (
                <tr key={product.id}>
                  <td>{product.id}</td>
                  <td>{product.name}</td>
                  <td>{product.altname || '-'}</td>
                  <td>RM {product.price.toFixed(2)}</td>
                  <td>{product.category || 'N/A'}</td>
                  <td>{product.isAvailable ? 'Available' : 'Not Available'}</td>
                  <td className="actions">
                    <button
                      onClick={() => navigate(`/products/${product.id}`)}
                      className="btn-view"
                    >
                      View
                    </button>
                    {isAdmin && (
                      <>
                        <button
                          onClick={() => navigate(`/products/edit/${product.id}`)}
                          className="btn-edit"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleToggleAvailability(product.id, product.isAvailable)}
                          className={product.isAvailable ? "btn-deactivate" : "btn-activate"}
                        >
                          {product.isAvailable ? 'Deactivate' : 'Activate'}
                        </button>
                      </>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default ProductList;

.order-detail-container {
  padding: 20px;
}

.order-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.order-actions {
  display: flex;
  gap: 10px;
}

.btn-back, .btn-edit, .btn-delete {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 14px;
}

.btn-back {
  background-color: #f2f2f2;
  color: #333;
}

.btn-edit {
  background-color: #FFC107;
  color: white;
}

.btn-delete {
  background-color: #F44336;
  color: white;
}

.btn-back:hover {
  background-color: #e0e0e0;
}

.btn-edit:hover {
  background-color: #e0a800;
}

.btn-delete:hover {
  background-color: #da190b;
}

.order-detail-card {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.order-header h3 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.status-badge {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  color: white;
}

.status-badge.pending {
  background-color: #FFC107;
}

.status-badge.completed {
  background-color: #4CAF50;
}

.status-badge.cancelled {
  background-color: #F44336;
}

.info-section {
  margin-bottom: 30px;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.info-label {
  font-weight: bold;
  width: 150px;
  color: #666;
}

.info-value {
  flex: 1;
}

.total-amount {
  font-weight: bold;
  font-size: 18px;
  color: #4CAF50;
}

.order-items {
  margin-top: 30px;
}

.order-items h4 {
  margin-top: 0;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.table-responsive {
  overflow-x: auto;
}

.items-table {
  width: 100%;
  border-collapse: collapse;
}

.items-table th, .items-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}

.items-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.items-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.total-label {
  text-align: right;
  font-weight: bold;
}

.total-value {
  font-weight: bold;
  font-size: 18px;
}

.no-items {
  padding: 20px;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.loading, .error, .not-found {
  padding: 20px;
  text-align: center;
}

.error {
  color: #F44336;
}

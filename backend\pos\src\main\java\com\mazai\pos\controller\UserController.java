package com.mazai.pos.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mazai.pos.model.User;
import com.mazai.pos.service.UserService;

@RestController
@RequestMapping("/api/users")
public class UserController {

    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    @GetMapping
    public ResponseEntity<List<User>> getAllUsers() {
        List<User> users = userService.getAllUsers();
        return new ResponseEntity<>(users, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        Optional<User> user = userService.getUserById(id);
        return user.map(value -> new ResponseEntity<>(value, HttpStatus.OK))
                .orElseGet(() -> new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    @PostMapping
    public ResponseEntity<User> createUser(@RequestBody User user) {
        logger.info("Creating new user: {}", user.getUsername());

        // Check if username or email already exists
        if (userService.existsByUsername(user.getUsername())) {
            logger.warn("Username already exists: {}", user.getUsername());
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
        if (userService.existsByEmail(user.getEmail())) {
            logger.warn("Email already exists: {}", user.getEmail());
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }

        User newUser = userService.createUser(user);
        logger.info("User created successfully: {} with role: {}", newUser.getUsername(), newUser.getRole());
        return new ResponseEntity<>(newUser, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    public ResponseEntity<User> updateUser(@PathVariable Long id, @RequestBody User user) {
        Optional<User> existingUser = userService.getUserById(id);

        if (existingUser.isPresent()) {
            // Check if username is being changed and already exists
            if (!existingUser.get().getUsername().equals(user.getUsername()) &&
                userService.existsByUsername(user.getUsername())) {
                return new ResponseEntity<>(HttpStatus.CONFLICT);
            }

            // Check if email is being changed and already exists
            if (!existingUser.get().getEmail().equals(user.getEmail()) &&
                userService.existsByEmail(user.getEmail())) {
                return new ResponseEntity<>(HttpStatus.CONFLICT);
            }

            user.setId(id);
            User updatedUser = userService.updateUser(user);
            return new ResponseEntity<>(updatedUser, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        logger.info("Deleting user with ID: {}", id);
        Optional<User> existingUser = userService.getUserById(id);

        if (existingUser.isPresent()) {
            userService.deleteUser(id);
            logger.info("User deleted successfully: {}", existingUser.get().getUsername());
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        } else {
            logger.warn("User not found for deletion with ID: {}", id);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }
}



2025-06-11 17:49:26.936 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 5980 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by <PERSON> in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 17:49:26.937 [restartedMain] DEBUG com.mazai.pos.PosApplication - Running with Spring Boot v3.4.5, Spring v6.2.6
2025-06-11 17:49:26.938 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 17:49:27.033 [restartedMain] INFO  o.s.b.d.restart.ChangeableUrls - The Class-Path manifest attribute in C:\Users\<USER>\.m2\repository\org\scijava\native-lib-loader\2.3.6\native-lib-loader-2.3.6.jar referenced one or more files that do not exist: file:/C:/Users/<USER>/.m2/repository/org/scijava/native-lib-loader/2.3.6/slf4j-api-1.7.30.jar
2025-06-11 17:49:27.037 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-11 17:49:27.037 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-11 17:49:28.159 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 17:49:28.348 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 166 ms. Found 7 JPA repository interfaces.
2025-06-11 17:49:29.305 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-11 17:49:29.324 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-11 17:49:29.328 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 17:49:29.329 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.40]
2025-06-11 17:49:29.405 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 17:49:29.405 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2367 ms
2025-06-11 17:49:29.582 [restartedMain] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 17:49:29.665 [restartedMain] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.13.Final
2025-06-11 17:49:29.711 [restartedMain] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-11 17:49:30.059 [restartedMain] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-11 17:49:30.646 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 17:49:30.664 [restartedMain] INFO  o.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'org.springframework.jdbc.datasource.DriverManagerDataSource@27f90b2e']
	Database driver: undefined/unknown
	Database version: 8.0.41
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-11 17:49:31.912 [restartedMain] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-11 17:49:32.137 [restartedMain] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 17:49:32.524 [restartedMain] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-11 17:49:32.598 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Global AuthenticationManager configured with AuthenticationProvider bean with name authenticationProvider
2025-06-11 17:49:32.598 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 17:49:32.789 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 17:49:37.801 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 17:49:37.801 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 17:49:37.900 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 17:49:38.604 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-11 17:49:38.621 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-11 17:49:38.646 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-06-11 17:49:38.654 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 12.466 seconds (process running for 13.091)
2025-06-11 18:32:58.102 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 18:32:58.103 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 18:32:58.106 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-11 18:32:58.173 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: POST /api/auth/login
2025-06-11 18:32:58.173 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request headers:
2025-06-11 18:32:58.173 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   host: localhost:8080
2025-06-11 18:32:58.173 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   connection: keep-alive
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   content-length: 42
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-platform: "Windows"
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0ODk2OTM4MiwiZXhwIjoxNzQ5MDU1NzgyfQ.iSJqKAPw4ye4T8HJIGVhQZG08fw-j1kGQnIZ1H50xrpz5VPo53k1hk9HYJG3Rn3ioRjVbYSyd1TxlSr3OkJ6nA
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept: application/json
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   content-type: application/json
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-mobile: ?0
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   origin: http://localhost:3000
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-site: same-site
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-mode: cors
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-dest: empty
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   referer: http://localhost:3000/
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-encoding: gzip, deflate, br, zstd
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-language: en-MY,en;q=0.9,zh-MY;q=0.8,zh;q=0.7,ms-MY;q=0.6,ms;q=0.5,en-US;q=0.4
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   cookie: ph_phc_t3lgBB66QsPW4HEfiGopO14um4XGNtBcefEKYWelWda_posthog=%7B%22%24sesid%22%3A%5B1749551724309%2C%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C1749551723559%5D%2C%22%24client_session_props%22%3A%7B%22sessionId%22%3A%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C%22props%22%3A%7B%22initialPathName%22%3A%22%2F%22%2C%22referringDomain%22%3A%22%24direct%22%7D%7D%2C%22distinct_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22%24device_id%22%3A%**********-b42d-7ad0-a57b-91d7953c591b%22%2C%22%24user_state%22%3A%22identified%22%2C%22%24epp%22%3Atrue%2C%22%24stored_group_properties%22%3A%7B%22cluster%22%3A%7B%22app_pathname%22%3A%22welcome%22%2C%22app_theme%22%3A%22light%22%2C%22cluster_creation_date%22%3A%222025-06-10T10%3A16%3A06.500Z%22%2C%22nodes_count%22%3A3%2C%22traffic_last_month%22%3A0%2C%22input_traffic_last_month%22%3A0%2C%22users_count%22%3A0%2C%22license_count%22%3A0%2C%22node_leader_app_version%22%3A%226.1.12%2B23f653e%22%2C%22installation_source%22%3A%22docker%22%2C%22nodes%22%3A%7B%2277BkJC_dSf-fmslRiZeKaQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22RWbZVAVESbG-6WYuipatjQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22u8f0KQAgRmioRFol9sDOHg%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%7D%2C%22plugin_is_enterprise_plugin_installed%22%3Afalse%2C%22plugins%22%3A%5B%22Elasticsearch%207%20Support%3A6.1.12%2B23f653e%22%2C%22Threat%20Intelligence%20Plugin%3A6.1.12%2B23f653e%22%2C%22Integrations%3A6.1.12%2B23f653e%22%2C%22OpenSearch%202%20Support%3A6.1.12%2B23f653e%22%2C%22AWS%20plugins%3A6.1.12%2B23f653e%22%5D%2C%22search_cluster_nodes_count%22%3A3%2C%22search_cluster_version%22%3A%22DataNode%3A6.1.12%2B23f653e%22%2C%22data_nodes_count%22%3A3%2C%22cluster_id%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%2C%22user%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22user_is_local_admin%22%3Atrue%2C%22user_role_count%22%3A1%2C%22user_team_count%22%3A0%7D%7D%2C%22%24groups%22%3A%7B%22cluster%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%7D%2C%22%24capture_rate_limit%22%3A%7B%22tokens%22%3A99%2C%22last%22%3A1749551724300%7D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A9000%2Fwelcome%22%7D%2C%22%24user_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%7D
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Authorization header: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0ODk2OTM4MiwiZXhwIjoxNzQ5MDU1NzgyfQ.iSJqKAPw4ye4T8HJIGVhQZG08fw-j1kGQnIZ1H50xrpz5VPo53k1hk9HYJG3Rn3ioRjVbYSyd1TxlSr3OkJ6nA
2025-06-11 18:32:58.174 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request method: POST
2025-06-11 18:32:58.175 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request URL: http://localhost:8080/api/auth/login
2025-06-11 18:32:58.175 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - All request headers:
2025-06-11 18:32:58.175 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   host: localhost:8080
2025-06-11 18:32:58.175 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   connection: keep-alive
2025-06-11 18:32:58.175 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   content-length: 42
2025-06-11 18:32:58.175 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-platform: "Windows"
2025-06-11 18:32:58.175 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0ODk2OTM4MiwiZXhwIjoxNzQ5MDU1NzgyfQ.iSJqKAPw4ye4T8HJIGVhQZG08fw-j1kGQnIZ1H50xrpz5VPo53k1hk9HYJG3Rn3ioRjVbYSyd1TxlSr3OkJ6nA
2025-06-11 18:32:58.175 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-11 18:32:58.175 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept: application/json
2025-06-11 18:32:58.175 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-11 18:32:58.175 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   content-type: application/json
2025-06-11 18:32:58.176 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-mobile: ?0
2025-06-11 18:32:58.176 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   origin: http://localhost:3000
2025-06-11 18:32:58.176 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-site: same-site
2025-06-11 18:32:58.176 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-mode: cors
2025-06-11 18:32:58.176 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-dest: empty
2025-06-11 18:32:58.176 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   referer: http://localhost:3000/
2025-06-11 18:32:58.176 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-encoding: gzip, deflate, br, zstd
2025-06-11 18:32:58.176 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-language: en-MY,en;q=0.9,zh-MY;q=0.8,zh;q=0.7,ms-MY;q=0.6,ms;q=0.5,en-US;q=0.4
2025-06-11 18:32:58.176 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   cookie: ph_phc_t3lgBB66QsPW4HEfiGopO14um4XGNtBcefEKYWelWda_posthog=%7B%22%24sesid%22%3A%5B1749551724309%2C%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C1749551723559%5D%2C%22%24client_session_props%22%3A%7B%22sessionId%22%3A%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C%22props%22%3A%7B%22initialPathName%22%3A%22%2F%22%2C%22referringDomain%22%3A%22%24direct%22%7D%7D%2C%22distinct_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22%24device_id%22%3A%**********-b42d-7ad0-a57b-91d7953c591b%22%2C%22%24user_state%22%3A%22identified%22%2C%22%24epp%22%3Atrue%2C%22%24stored_group_properties%22%3A%7B%22cluster%22%3A%7B%22app_pathname%22%3A%22welcome%22%2C%22app_theme%22%3A%22light%22%2C%22cluster_creation_date%22%3A%222025-06-10T10%3A16%3A06.500Z%22%2C%22nodes_count%22%3A3%2C%22traffic_last_month%22%3A0%2C%22input_traffic_last_month%22%3A0%2C%22users_count%22%3A0%2C%22license_count%22%3A0%2C%22node_leader_app_version%22%3A%226.1.12%2B23f653e%22%2C%22installation_source%22%3A%22docker%22%2C%22nodes%22%3A%7B%2277BkJC_dSf-fmslRiZeKaQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22RWbZVAVESbG-6WYuipatjQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22u8f0KQAgRmioRFol9sDOHg%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%7D%2C%22plugin_is_enterprise_plugin_installed%22%3Afalse%2C%22plugins%22%3A%5B%22Elasticsearch%207%20Support%3A6.1.12%2B23f653e%22%2C%22Threat%20Intelligence%20Plugin%3A6.1.12%2B23f653e%22%2C%22Integrations%3A6.1.12%2B23f653e%22%2C%22OpenSearch%202%20Support%3A6.1.12%2B23f653e%22%2C%22AWS%20plugins%3A6.1.12%2B23f653e%22%5D%2C%22search_cluster_nodes_count%22%3A3%2C%22search_cluster_version%22%3A%22DataNode%3A6.1.12%2B23f653e%22%2C%22data_nodes_count%22%3A3%2C%22cluster_id%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%2C%22user%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22user_is_local_admin%22%3Atrue%2C%22user_role_count%22%3A1%2C%22user_team_count%22%3A0%7D%7D%2C%22%24groups%22%3A%7B%22cluster%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%7D%2C%22%24capture_rate_limit%22%3A%7B%22tokens%22%3A99%2C%22last%22%3A1749551724300%7D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A9000%2Fwelcome%22%7D%2C%22%24user_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%7D
2025-06-11 18:32:58.176 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Extracted JWT token: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0ODk2OTM4MiwiZXhwIjoxNzQ5MDU1NzgyfQ.iSJqKAPw4ye4T8HJIGVhQZG08fw-j1kGQnIZ1H50xrpz5VPo53k1hk9HYJG3Rn3ioRjVbYSyd1TxlSr3OkJ6nA
2025-06-11 18:32:58.176 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token found, length: 174
2025-06-11 18:32:58.178 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token first 20 chars: eyJhbGciOiJIUzUxMiJ9...
2025-06-11 18:32:58.178 [http-nio-8080-exec-2] DEBUG com.mazai.pos.security.jwt.JwtUtils - Validating JWT token, length: 174
2025-06-11 18:32:58.178 [http-nio-8080-exec-2] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT token first 20 chars: eyJhbGciOiJIUzUxMiJ9...
2025-06-11 18:32:58.178 [http-nio-8080-exec-2] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT structure appears valid (has 3 parts)
2025-06-11 18:32:58.178 [http-nio-8080-exec-2] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT header: {"alg":"HS512"}
2025-06-11 18:32:58.179 [http-nio-8080-exec-2] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT payload: {"sub":"admin","iat":1748969382,"exp":1749055782}
2025-06-11 18:32:58.179 [http-nio-8080-exec-2] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT expiration time: 1749055782, current time: 1749637978, diff: -582196 seconds
2025-06-11 18:32:58.179 [http-nio-8080-exec-2] ERROR com.mazai.pos.security.jwt.JwtUtils - JWT token is expired: exp=1749055782, now=1749637978
2025-06-11 18:32:58.179 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token valid: false
2025-06-11 18:32:58.179 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token is invalid, not setting authentication
2025-06-11 18:32:58.179 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT structure appears valid (has 3 parts)
2025-06-11 18:32:58.179 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT header: {"alg":"HS512"}
2025-06-11 18:32:58.179 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT payload: {"sub":"admin","iat":1748969382,"exp":1749055782}
2025-06-11 18:32:58.361 [http-nio-8080-exec-2] DEBUG c.m.p.s.s.UserDetailsServiceImpl - Loading user details for username: admin
2025-06-11 18:32:58.560 [http-nio-8080-exec-2] DEBUG c.m.p.s.s.UserDetailsServiceImpl - User found: id=4, username=admin, role=ADMIN
2025-06-11 18:32:58.564 [http-nio-8080-exec-2] DEBUG c.m.p.s.s.UserDetailsServiceImpl - UserDetails built successfully for username: admin
2025-06-11 18:32:58.648 [http-nio-8080-exec-2] WARN  c.m.pos.controller.AuthController - Bad credentials for user: admin
2025-06-11 18:33:03.332 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: POST /api/auth/login
2025-06-11 18:33:03.333 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request headers:
2025-06-11 18:33:03.333 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   host: localhost:8080
2025-06-11 18:33:03.333 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   connection: keep-alive
2025-06-11 18:33:03.333 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   content-length: 42
2025-06-11 18:33:03.333 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-platform: "Windows"
2025-06-11 18:33:03.333 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0ODk2OTM4MiwiZXhwIjoxNzQ5MDU1NzgyfQ.iSJqKAPw4ye4T8HJIGVhQZG08fw-j1kGQnIZ1H50xrpz5VPo53k1hk9HYJG3Rn3ioRjVbYSyd1TxlSr3OkJ6nA
2025-06-11 18:33:03.333 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-11 18:33:03.333 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept: application/json
2025-06-11 18:33:03.333 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-11 18:33:03.333 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   content-type: application/json
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-mobile: ?0
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   origin: http://localhost:3000
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-site: same-site
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-mode: cors
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-dest: empty
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   referer: http://localhost:3000/
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-encoding: gzip, deflate, br, zstd
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-language: en-MY,en;q=0.9,zh-MY;q=0.8,zh;q=0.7,ms-MY;q=0.6,ms;q=0.5,en-US;q=0.4
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   cookie: ph_phc_t3lgBB66QsPW4HEfiGopO14um4XGNtBcefEKYWelWda_posthog=%7B%22%24sesid%22%3A%5B1749551724309%2C%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C1749551723559%5D%2C%22%24client_session_props%22%3A%7B%22sessionId%22%3A%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C%22props%22%3A%7B%22initialPathName%22%3A%22%2F%22%2C%22referringDomain%22%3A%22%24direct%22%7D%7D%2C%22distinct_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22%24device_id%22%3A%**********-b42d-7ad0-a57b-91d7953c591b%22%2C%22%24user_state%22%3A%22identified%22%2C%22%24epp%22%3Atrue%2C%22%24stored_group_properties%22%3A%7B%22cluster%22%3A%7B%22app_pathname%22%3A%22welcome%22%2C%22app_theme%22%3A%22light%22%2C%22cluster_creation_date%22%3A%222025-06-10T10%3A16%3A06.500Z%22%2C%22nodes_count%22%3A3%2C%22traffic_last_month%22%3A0%2C%22input_traffic_last_month%22%3A0%2C%22users_count%22%3A0%2C%22license_count%22%3A0%2C%22node_leader_app_version%22%3A%226.1.12%2B23f653e%22%2C%22installation_source%22%3A%22docker%22%2C%22nodes%22%3A%7B%2277BkJC_dSf-fmslRiZeKaQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22RWbZVAVESbG-6WYuipatjQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22u8f0KQAgRmioRFol9sDOHg%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%7D%2C%22plugin_is_enterprise_plugin_installed%22%3Afalse%2C%22plugins%22%3A%5B%22Elasticsearch%207%20Support%3A6.1.12%2B23f653e%22%2C%22Threat%20Intelligence%20Plugin%3A6.1.12%2B23f653e%22%2C%22Integrations%3A6.1.12%2B23f653e%22%2C%22OpenSearch%202%20Support%3A6.1.12%2B23f653e%22%2C%22AWS%20plugins%3A6.1.12%2B23f653e%22%5D%2C%22search_cluster_nodes_count%22%3A3%2C%22search_cluster_version%22%3A%22DataNode%3A6.1.12%2B23f653e%22%2C%22data_nodes_count%22%3A3%2C%22cluster_id%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%2C%22user%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22user_is_local_admin%22%3Atrue%2C%22user_role_count%22%3A1%2C%22user_team_count%22%3A0%7D%7D%2C%22%24groups%22%3A%7B%22cluster%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%7D%2C%22%24capture_rate_limit%22%3A%7B%22tokens%22%3A99%2C%22last%22%3A1749551724300%7D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A9000%2Fwelcome%22%7D%2C%22%24user_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%7D
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Authorization header: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0ODk2OTM4MiwiZXhwIjoxNzQ5MDU1NzgyfQ.iSJqKAPw4ye4T8HJIGVhQZG08fw-j1kGQnIZ1H50xrpz5VPo53k1hk9HYJG3Rn3ioRjVbYSyd1TxlSr3OkJ6nA
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request method: POST
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request URL: http://localhost:8080/api/auth/login
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - All request headers:
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   host: localhost:8080
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   connection: keep-alive
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   content-length: 42
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-platform: "Windows"
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0ODk2OTM4MiwiZXhwIjoxNzQ5MDU1NzgyfQ.iSJqKAPw4ye4T8HJIGVhQZG08fw-j1kGQnIZ1H50xrpz5VPo53k1hk9HYJG3Rn3ioRjVbYSyd1TxlSr3OkJ6nA
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept: application/json
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   content-type: application/json
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-mobile: ?0
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   origin: http://localhost:3000
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-site: same-site
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-mode: cors
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-dest: empty
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   referer: http://localhost:3000/
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-encoding: gzip, deflate, br, zstd
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-language: en-MY,en;q=0.9,zh-MY;q=0.8,zh;q=0.7,ms-MY;q=0.6,ms;q=0.5,en-US;q=0.4
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   cookie: ph_phc_t3lgBB66QsPW4HEfiGopO14um4XGNtBcefEKYWelWda_posthog=%7B%22%24sesid%22%3A%5B1749551724309%2C%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C1749551723559%5D%2C%22%24client_session_props%22%3A%7B%22sessionId%22%3A%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C%22props%22%3A%7B%22initialPathName%22%3A%22%2F%22%2C%22referringDomain%22%3A%22%24direct%22%7D%7D%2C%22distinct_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22%24device_id%22%3A%**********-b42d-7ad0-a57b-91d7953c591b%22%2C%22%24user_state%22%3A%22identified%22%2C%22%24epp%22%3Atrue%2C%22%24stored_group_properties%22%3A%7B%22cluster%22%3A%7B%22app_pathname%22%3A%22welcome%22%2C%22app_theme%22%3A%22light%22%2C%22cluster_creation_date%22%3A%222025-06-10T10%3A16%3A06.500Z%22%2C%22nodes_count%22%3A3%2C%22traffic_last_month%22%3A0%2C%22input_traffic_last_month%22%3A0%2C%22users_count%22%3A0%2C%22license_count%22%3A0%2C%22node_leader_app_version%22%3A%226.1.12%2B23f653e%22%2C%22installation_source%22%3A%22docker%22%2C%22nodes%22%3A%7B%2277BkJC_dSf-fmslRiZeKaQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22RWbZVAVESbG-6WYuipatjQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22u8f0KQAgRmioRFol9sDOHg%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%7D%2C%22plugin_is_enterprise_plugin_installed%22%3Afalse%2C%22plugins%22%3A%5B%22Elasticsearch%207%20Support%3A6.1.12%2B23f653e%22%2C%22Threat%20Intelligence%20Plugin%3A6.1.12%2B23f653e%22%2C%22Integrations%3A6.1.12%2B23f653e%22%2C%22OpenSearch%202%20Support%3A6.1.12%2B23f653e%22%2C%22AWS%20plugins%3A6.1.12%2B23f653e%22%5D%2C%22search_cluster_nodes_count%22%3A3%2C%22search_cluster_version%22%3A%22DataNode%3A6.1.12%2B23f653e%22%2C%22data_nodes_count%22%3A3%2C%22cluster_id%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%2C%22user%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22user_is_local_admin%22%3Atrue%2C%22user_role_count%22%3A1%2C%22user_team_count%22%3A0%7D%7D%2C%22%24groups%22%3A%7B%22cluster%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%7D%2C%22%24capture_rate_limit%22%3A%7B%22tokens%22%3A99%2C%22last%22%3A1749551724300%7D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A9000%2Fwelcome%22%7D%2C%22%24user_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%7D
2025-06-11 18:33:03.334 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Extracted JWT token: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0ODk2OTM4MiwiZXhwIjoxNzQ5MDU1NzgyfQ.iSJqKAPw4ye4T8HJIGVhQZG08fw-j1kGQnIZ1H50xrpz5VPo53k1hk9HYJG3Rn3ioRjVbYSyd1TxlSr3OkJ6nA
2025-06-11 18:33:03.335 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token found, length: 174
2025-06-11 18:33:03.335 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token first 20 chars: eyJhbGciOiJIUzUxMiJ9...
2025-06-11 18:33:03.335 [http-nio-8080-exec-3] DEBUG com.mazai.pos.security.jwt.JwtUtils - Validating JWT token, length: 174
2025-06-11 18:33:03.335 [http-nio-8080-exec-3] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT token first 20 chars: eyJhbGciOiJIUzUxMiJ9...
2025-06-11 18:33:03.335 [http-nio-8080-exec-3] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT structure appears valid (has 3 parts)
2025-06-11 18:33:03.335 [http-nio-8080-exec-3] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT header: {"alg":"HS512"}
2025-06-11 18:33:03.335 [http-nio-8080-exec-3] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT payload: {"sub":"admin","iat":1748969382,"exp":1749055782}
2025-06-11 18:33:03.335 [http-nio-8080-exec-3] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT expiration time: 1749055782, current time: 1749637983, diff: -582201 seconds
2025-06-11 18:33:03.335 [http-nio-8080-exec-3] ERROR com.mazai.pos.security.jwt.JwtUtils - JWT token is expired: exp=1749055782, now=1749637983
2025-06-11 18:33:03.335 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token valid: false
2025-06-11 18:33:03.335 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token is invalid, not setting authentication
2025-06-11 18:33:03.335 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT structure appears valid (has 3 parts)
2025-06-11 18:33:03.336 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT header: {"alg":"HS512"}
2025-06-11 18:33:03.336 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT payload: {"sub":"admin","iat":1748969382,"exp":1749055782}
2025-06-11 18:33:03.339 [http-nio-8080-exec-3] DEBUG c.m.p.s.s.UserDetailsServiceImpl - Loading user details for username: admin
2025-06-11 18:33:03.342 [http-nio-8080-exec-3] DEBUG c.m.p.s.s.UserDetailsServiceImpl - User found: id=4, username=admin, role=ADMIN
2025-06-11 18:33:03.342 [http-nio-8080-exec-3] DEBUG c.m.p.s.s.UserDetailsServiceImpl - UserDetails built successfully for username: admin
2025-06-11 18:33:03.410 [http-nio-8080-exec-3] DEBUG com.mazai.pos.security.jwt.JwtUtils - Generating JWT token for user: admin
2025-06-11 18:33:03.410 [http-nio-8080-exec-3] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT expiration: 86400000 ms
2025-06-11 18:33:03.427 [http-nio-8080-exec-3] INFO  com.mazai.pos.security.jwt.JwtUtils - Using configured key for HS512 algorithm
2025-06-11 18:33:03.447 [http-nio-8080-exec-3] DEBUG com.mazai.pos.security.jwt.JwtUtils - Generated JWT token: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.464 [http-nio-8080-exec-3] INFO  c.m.pos.controller.AuthController - User admin logged in successfully
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /api/test/auth-status
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request headers:
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   host: localhost:8080
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   connection: keep-alive
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-platform: "Windows"
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept: application/json
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-mobile: ?0
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   origin: http://localhost:3000
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-site: same-site
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-mode: cors
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-dest: empty
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   referer: http://localhost:3000/
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-encoding: gzip, deflate, br, zstd
2025-06-11 18:33:03.477 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-language: en-MY,en;q=0.9,zh-MY;q=0.8,zh;q=0.7,ms-MY;q=0.6,ms;q=0.5,en-US;q=0.4
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   cookie: ph_phc_t3lgBB66QsPW4HEfiGopO14um4XGNtBcefEKYWelWda_posthog=%7B%22%24sesid%22%3A%5B1749551724309%2C%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C1749551723559%5D%2C%22%24client_session_props%22%3A%7B%22sessionId%22%3A%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C%22props%22%3A%7B%22initialPathName%22%3A%22%2F%22%2C%22referringDomain%22%3A%22%24direct%22%7D%7D%2C%22distinct_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22%24device_id%22%3A%**********-b42d-7ad0-a57b-91d7953c591b%22%2C%22%24user_state%22%3A%22identified%22%2C%22%24epp%22%3Atrue%2C%22%24stored_group_properties%22%3A%7B%22cluster%22%3A%7B%22app_pathname%22%3A%22welcome%22%2C%22app_theme%22%3A%22light%22%2C%22cluster_creation_date%22%3A%222025-06-10T10%3A16%3A06.500Z%22%2C%22nodes_count%22%3A3%2C%22traffic_last_month%22%3A0%2C%22input_traffic_last_month%22%3A0%2C%22users_count%22%3A0%2C%22license_count%22%3A0%2C%22node_leader_app_version%22%3A%226.1.12%2B23f653e%22%2C%22installation_source%22%3A%22docker%22%2C%22nodes%22%3A%7B%2277BkJC_dSf-fmslRiZeKaQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22RWbZVAVESbG-6WYuipatjQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22u8f0KQAgRmioRFol9sDOHg%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%7D%2C%22plugin_is_enterprise_plugin_installed%22%3Afalse%2C%22plugins%22%3A%5B%22Elasticsearch%207%20Support%3A6.1.12%2B23f653e%22%2C%22Threat%20Intelligence%20Plugin%3A6.1.12%2B23f653e%22%2C%22Integrations%3A6.1.12%2B23f653e%22%2C%22OpenSearch%202%20Support%3A6.1.12%2B23f653e%22%2C%22AWS%20plugins%3A6.1.12%2B23f653e%22%5D%2C%22search_cluster_nodes_count%22%3A3%2C%22search_cluster_version%22%3A%22DataNode%3A6.1.12%2B23f653e%22%2C%22data_nodes_count%22%3A3%2C%22cluster_id%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%2C%22user%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22user_is_local_admin%22%3Atrue%2C%22user_role_count%22%3A1%2C%22user_team_count%22%3A0%7D%7D%2C%22%24groups%22%3A%7B%22cluster%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%7D%2C%22%24capture_rate_limit%22%3A%7B%22tokens%22%3A99%2C%22last%22%3A1749551724300%7D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A9000%2Fwelcome%22%7D%2C%22%24user_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%7D
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Authorization header: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request method: GET
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request URL: http://localhost:8080/api/test/auth-status
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - All request headers:
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   host: localhost:8080
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   connection: keep-alive
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-platform: "Windows"
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept: application/json
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-mobile: ?0
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   origin: http://localhost:3000
2025-06-11 18:33:03.478 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-site: same-site
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-mode: cors
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-dest: empty
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   referer: http://localhost:3000/
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-encoding: gzip, deflate, br, zstd
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-language: en-MY,en;q=0.9,zh-MY;q=0.8,zh;q=0.7,ms-MY;q=0.6,ms;q=0.5,en-US;q=0.4
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   cookie: ph_phc_t3lgBB66QsPW4HEfiGopO14um4XGNtBcefEKYWelWda_posthog=%7B%22%24sesid%22%3A%5B1749551724309%2C%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C1749551723559%5D%2C%22%24client_session_props%22%3A%7B%22sessionId%22%3A%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C%22props%22%3A%7B%22initialPathName%22%3A%22%2F%22%2C%22referringDomain%22%3A%22%24direct%22%7D%7D%2C%22distinct_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22%24device_id%22%3A%**********-b42d-7ad0-a57b-91d7953c591b%22%2C%22%24user_state%22%3A%22identified%22%2C%22%24epp%22%3Atrue%2C%22%24stored_group_properties%22%3A%7B%22cluster%22%3A%7B%22app_pathname%22%3A%22welcome%22%2C%22app_theme%22%3A%22light%22%2C%22cluster_creation_date%22%3A%222025-06-10T10%3A16%3A06.500Z%22%2C%22nodes_count%22%3A3%2C%22traffic_last_month%22%3A0%2C%22input_traffic_last_month%22%3A0%2C%22users_count%22%3A0%2C%22license_count%22%3A0%2C%22node_leader_app_version%22%3A%226.1.12%2B23f653e%22%2C%22installation_source%22%3A%22docker%22%2C%22nodes%22%3A%7B%2277BkJC_dSf-fmslRiZeKaQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22RWbZVAVESbG-6WYuipatjQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22u8f0KQAgRmioRFol9sDOHg%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%7D%2C%22plugin_is_enterprise_plugin_installed%22%3Afalse%2C%22plugins%22%3A%5B%22Elasticsearch%207%20Support%3A6.1.12%2B23f653e%22%2C%22Threat%20Intelligence%20Plugin%3A6.1.12%2B23f653e%22%2C%22Integrations%3A6.1.12%2B23f653e%22%2C%22OpenSearch%202%20Support%3A6.1.12%2B23f653e%22%2C%22AWS%20plugins%3A6.1.12%2B23f653e%22%5D%2C%22search_cluster_nodes_count%22%3A3%2C%22search_cluster_version%22%3A%22DataNode%3A6.1.12%2B23f653e%22%2C%22data_nodes_count%22%3A3%2C%22cluster_id%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%2C%22user%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22user_is_local_admin%22%3Atrue%2C%22user_role_count%22%3A1%2C%22user_team_count%22%3A0%7D%7D%2C%22%24groups%22%3A%7B%22cluster%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%7D%2C%22%24capture_rate_limit%22%3A%7B%22tokens%22%3A99%2C%22last%22%3A1749551724300%7D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A9000%2Fwelcome%22%7D%2C%22%24user_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%7D
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Extracted JWT token: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token found, length: 174
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token first 20 chars: eyJhbGciOiJIUzUxMiJ9...
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG com.mazai.pos.security.jwt.JwtUtils - Validating JWT token, length: 174
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT token first 20 chars: eyJhbGciOiJIUzUxMiJ9...
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT structure appears valid (has 3 parts)
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT header: {"alg":"HS512"}
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT payload: {"sub":"admin","iat":1749637983,"exp":1749724383}
2025-06-11 18:33:03.479 [http-nio-8080-exec-5] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT expiration time: 1749724383, current time: 1749637983, diff: 86400 seconds
2025-06-11 18:33:03.500 [http-nio-8080-exec-5] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT token is valid
2025-06-11 18:33:03.500 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token valid: true
2025-06-11 18:33:03.502 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Username from JWT: admin
2025-06-11 18:33:03.506 [http-nio-8080-exec-5] DEBUG c.m.p.s.s.UserDetailsServiceImpl - Loading user details for username: admin
2025-06-11 18:33:03.508 [http-nio-8080-exec-5] DEBUG c.m.p.s.s.UserDetailsServiceImpl - User found: id=4, username=admin, role=ADMIN
2025-06-11 18:33:03.509 [http-nio-8080-exec-5] DEBUG c.m.p.s.s.UserDetailsServiceImpl - UserDetails built successfully for username: admin
2025-06-11 18:33:03.510 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - User details loaded for username: admin
2025-06-11 18:33:03.510 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - User authorities: [ADMIN]
2025-06-11 18:33:03.510 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Authentication set in SecurityContext for user: admin
2025-06-11 18:33:03.536 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /api/products
2025-06-11 18:33:03.536 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request headers:
2025-06-11 18:33:03.536 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   host: localhost:8080
2025-06-11 18:33:03.536 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   connection: keep-alive
2025-06-11 18:33:03.536 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-platform: "Windows"
2025-06-11 18:33:03.536 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.536 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-11 18:33:03.536 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept: application/json
2025-06-11 18:33:03.536 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-11 18:33:03.536 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-mobile: ?0
2025-06-11 18:33:03.536 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   origin: http://localhost:3000
2025-06-11 18:33:03.536 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-site: same-site
2025-06-11 18:33:03.536 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-mode: cors
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-dest: empty
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   referer: http://localhost:3000/
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-encoding: gzip, deflate, br, zstd
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-language: en-MY,en;q=0.9,zh-MY;q=0.8,zh;q=0.7,ms-MY;q=0.6,ms;q=0.5,en-US;q=0.4
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   cookie: ph_phc_t3lgBB66QsPW4HEfiGopO14um4XGNtBcefEKYWelWda_posthog=%7B%22%24sesid%22%3A%5B1749551724309%2C%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C1749551723559%5D%2C%22%24client_session_props%22%3A%7B%22sessionId%22%3A%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C%22props%22%3A%7B%22initialPathName%22%3A%22%2F%22%2C%22referringDomain%22%3A%22%24direct%22%7D%7D%2C%22distinct_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22%24device_id%22%3A%**********-b42d-7ad0-a57b-91d7953c591b%22%2C%22%24user_state%22%3A%22identified%22%2C%22%24epp%22%3Atrue%2C%22%24stored_group_properties%22%3A%7B%22cluster%22%3A%7B%22app_pathname%22%3A%22welcome%22%2C%22app_theme%22%3A%22light%22%2C%22cluster_creation_date%22%3A%222025-06-10T10%3A16%3A06.500Z%22%2C%22nodes_count%22%3A3%2C%22traffic_last_month%22%3A0%2C%22input_traffic_last_month%22%3A0%2C%22users_count%22%3A0%2C%22license_count%22%3A0%2C%22node_leader_app_version%22%3A%226.1.12%2B23f653e%22%2C%22installation_source%22%3A%22docker%22%2C%22nodes%22%3A%7B%2277BkJC_dSf-fmslRiZeKaQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22RWbZVAVESbG-6WYuipatjQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22u8f0KQAgRmioRFol9sDOHg%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%7D%2C%22plugin_is_enterprise_plugin_installed%22%3Afalse%2C%22plugins%22%3A%5B%22Elasticsearch%207%20Support%3A6.1.12%2B23f653e%22%2C%22Threat%20Intelligence%20Plugin%3A6.1.12%2B23f653e%22%2C%22Integrations%3A6.1.12%2B23f653e%22%2C%22OpenSearch%202%20Support%3A6.1.12%2B23f653e%22%2C%22AWS%20plugins%3A6.1.12%2B23f653e%22%5D%2C%22search_cluster_nodes_count%22%3A3%2C%22search_cluster_version%22%3A%22DataNode%3A6.1.12%2B23f653e%22%2C%22data_nodes_count%22%3A3%2C%22cluster_id%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%2C%22user%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22user_is_local_admin%22%3Atrue%2C%22user_role_count%22%3A1%2C%22user_team_count%22%3A0%7D%7D%2C%22%24groups%22%3A%7B%22cluster%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%7D%2C%22%24capture_rate_limit%22%3A%7B%22tokens%22%3A99%2C%22last%22%3A1749551724300%7D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A9000%2Fwelcome%22%7D%2C%22%24user_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%7D
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Authorization header: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request method: GET
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request URL: http://localhost:8080/api/products
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - All request headers:
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   host: localhost:8080
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   connection: keep-alive
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-platform: "Windows"
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept: application/json
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-mobile: ?0
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   origin: http://localhost:3000
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-site: same-site
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-mode: cors
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-dest: empty
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   referer: http://localhost:3000/
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-encoding: gzip, deflate, br, zstd
2025-06-11 18:33:03.537 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-language: en-MY,en;q=0.9,zh-MY;q=0.8,zh;q=0.7,ms-MY;q=0.6,ms;q=0.5,en-US;q=0.4
2025-06-11 18:33:03.538 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   cookie: ph_phc_t3lgBB66QsPW4HEfiGopO14um4XGNtBcefEKYWelWda_posthog=%7B%22%24sesid%22%3A%5B1749551724309%2C%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C1749551723559%5D%2C%22%24client_session_props%22%3A%7B%22sessionId%22%3A%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C%22props%22%3A%7B%22initialPathName%22%3A%22%2F%22%2C%22referringDomain%22%3A%22%24direct%22%7D%7D%2C%22distinct_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22%24device_id%22%3A%**********-b42d-7ad0-a57b-91d7953c591b%22%2C%22%24user_state%22%3A%22identified%22%2C%22%24epp%22%3Atrue%2C%22%24stored_group_properties%22%3A%7B%22cluster%22%3A%7B%22app_pathname%22%3A%22welcome%22%2C%22app_theme%22%3A%22light%22%2C%22cluster_creation_date%22%3A%222025-06-10T10%3A16%3A06.500Z%22%2C%22nodes_count%22%3A3%2C%22traffic_last_month%22%3A0%2C%22input_traffic_last_month%22%3A0%2C%22users_count%22%3A0%2C%22license_count%22%3A0%2C%22node_leader_app_version%22%3A%226.1.12%2B23f653e%22%2C%22installation_source%22%3A%22docker%22%2C%22nodes%22%3A%7B%2277BkJC_dSf-fmslRiZeKaQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22RWbZVAVESbG-6WYuipatjQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22u8f0KQAgRmioRFol9sDOHg%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%7D%2C%22plugin_is_enterprise_plugin_installed%22%3Afalse%2C%22plugins%22%3A%5B%22Elasticsearch%207%20Support%3A6.1.12%2B23f653e%22%2C%22Threat%20Intelligence%20Plugin%3A6.1.12%2B23f653e%22%2C%22Integrations%3A6.1.12%2B23f653e%22%2C%22OpenSearch%202%20Support%3A6.1.12%2B23f653e%22%2C%22AWS%20plugins%3A6.1.12%2B23f653e%22%5D%2C%22search_cluster_nodes_count%22%3A3%2C%22search_cluster_version%22%3A%22DataNode%3A6.1.12%2B23f653e%22%2C%22data_nodes_count%22%3A3%2C%22cluster_id%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%2C%22user%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22user_is_local_admin%22%3Atrue%2C%22user_role_count%22%3A1%2C%22user_team_count%22%3A0%7D%7D%2C%22%24groups%22%3A%7B%22cluster%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%7D%2C%22%24capture_rate_limit%22%3A%7B%22tokens%22%3A99%2C%22last%22%3A1749551724300%7D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A9000%2Fwelcome%22%7D%2C%22%24user_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%7D
2025-06-11 18:33:03.538 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Extracted JWT token: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.538 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token found, length: 174
2025-06-11 18:33:03.538 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token first 20 chars: eyJhbGciOiJIUzUxMiJ9...
2025-06-11 18:33:03.538 [http-nio-8080-exec-8] DEBUG com.mazai.pos.security.jwt.JwtUtils - Validating JWT token, length: 174
2025-06-11 18:33:03.538 [http-nio-8080-exec-8] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT token first 20 chars: eyJhbGciOiJIUzUxMiJ9...
2025-06-11 18:33:03.538 [http-nio-8080-exec-8] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT structure appears valid (has 3 parts)
2025-06-11 18:33:03.538 [http-nio-8080-exec-8] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT header: {"alg":"HS512"}
2025-06-11 18:33:03.538 [http-nio-8080-exec-8] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT payload: {"sub":"admin","iat":1749637983,"exp":1749724383}
2025-06-11 18:33:03.538 [http-nio-8080-exec-8] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT expiration time: 1749724383, current time: 1749637983, diff: 86400 seconds
2025-06-11 18:33:03.540 [http-nio-8080-exec-8] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT token is valid
2025-06-11 18:33:03.540 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token valid: true
2025-06-11 18:33:03.542 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Username from JWT: admin
2025-06-11 18:33:03.545 [http-nio-8080-exec-8] DEBUG c.m.p.s.s.UserDetailsServiceImpl - Loading user details for username: admin
2025-06-11 18:33:03.548 [http-nio-8080-exec-8] DEBUG c.m.p.s.s.UserDetailsServiceImpl - User found: id=4, username=admin, role=ADMIN
2025-06-11 18:33:03.548 [http-nio-8080-exec-8] DEBUG c.m.p.s.s.UserDetailsServiceImpl - UserDetails built successfully for username: admin
2025-06-11 18:33:03.550 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - User details loaded for username: admin
2025-06-11 18:33:03.550 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - User authorities: [ADMIN]
2025-06-11 18:33:03.550 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Authentication set in SecurityContext for user: admin
2025-06-11 18:33:03.598 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /api/products
2025-06-11 18:33:03.598 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request headers:
2025-06-11 18:33:03.598 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   host: localhost:8080
2025-06-11 18:33:03.598 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   connection: keep-alive
2025-06-11 18:33:03.598 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-platform: "Windows"
2025-06-11 18:33:03.598 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.598 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept: application/json
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-mobile: ?0
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   origin: http://localhost:3000
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-site: same-site
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-mode: cors
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-dest: empty
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   referer: http://localhost:3000/
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-encoding: gzip, deflate, br, zstd
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-language: en-MY,en;q=0.9,zh-MY;q=0.8,zh;q=0.7,ms-MY;q=0.6,ms;q=0.5,en-US;q=0.4
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   cookie: ph_phc_t3lgBB66QsPW4HEfiGopO14um4XGNtBcefEKYWelWda_posthog=%7B%22%24sesid%22%3A%5B1749551724309%2C%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C1749551723559%5D%2C%22%24client_session_props%22%3A%7B%22sessionId%22%3A%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C%22props%22%3A%7B%22initialPathName%22%3A%22%2F%22%2C%22referringDomain%22%3A%22%24direct%22%7D%7D%2C%22distinct_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22%24device_id%22%3A%**********-b42d-7ad0-a57b-91d7953c591b%22%2C%22%24user_state%22%3A%22identified%22%2C%22%24epp%22%3Atrue%2C%22%24stored_group_properties%22%3A%7B%22cluster%22%3A%7B%22app_pathname%22%3A%22welcome%22%2C%22app_theme%22%3A%22light%22%2C%22cluster_creation_date%22%3A%222025-06-10T10%3A16%3A06.500Z%22%2C%22nodes_count%22%3A3%2C%22traffic_last_month%22%3A0%2C%22input_traffic_last_month%22%3A0%2C%22users_count%22%3A0%2C%22license_count%22%3A0%2C%22node_leader_app_version%22%3A%226.1.12%2B23f653e%22%2C%22installation_source%22%3A%22docker%22%2C%22nodes%22%3A%7B%2277BkJC_dSf-fmslRiZeKaQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22RWbZVAVESbG-6WYuipatjQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22u8f0KQAgRmioRFol9sDOHg%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%7D%2C%22plugin_is_enterprise_plugin_installed%22%3Afalse%2C%22plugins%22%3A%5B%22Elasticsearch%207%20Support%3A6.1.12%2B23f653e%22%2C%22Threat%20Intelligence%20Plugin%3A6.1.12%2B23f653e%22%2C%22Integrations%3A6.1.12%2B23f653e%22%2C%22OpenSearch%202%20Support%3A6.1.12%2B23f653e%22%2C%22AWS%20plugins%3A6.1.12%2B23f653e%22%5D%2C%22search_cluster_nodes_count%22%3A3%2C%22search_cluster_version%22%3A%22DataNode%3A6.1.12%2B23f653e%22%2C%22data_nodes_count%22%3A3%2C%22cluster_id%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%2C%22user%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22user_is_local_admin%22%3Atrue%2C%22user_role_count%22%3A1%2C%22user_team_count%22%3A0%7D%7D%2C%22%24groups%22%3A%7B%22cluster%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%7D%2C%22%24capture_rate_limit%22%3A%7B%22tokens%22%3A99%2C%22last%22%3A1749551724300%7D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A9000%2Fwelcome%22%7D%2C%22%24user_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%7D
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Authorization header: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request method: GET
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request URL: http://localhost:8080/api/products
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - All request headers:
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   host: localhost:8080
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   connection: keep-alive
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-platform: "Windows"
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept: application/json
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-mobile: ?0
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   origin: http://localhost:3000
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-site: same-site
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-mode: cors
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-dest: empty
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   referer: http://localhost:3000/
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-encoding: gzip, deflate, br, zstd
2025-06-11 18:33:03.599 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-language: en-MY,en;q=0.9,zh-MY;q=0.8,zh;q=0.7,ms-MY;q=0.6,ms;q=0.5,en-US;q=0.4
2025-06-11 18:33:03.600 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   cookie: ph_phc_t3lgBB66QsPW4HEfiGopO14um4XGNtBcefEKYWelWda_posthog=%7B%22%24sesid%22%3A%5B1749551724309%2C%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C1749551723559%5D%2C%22%24client_session_props%22%3A%7B%22sessionId%22%3A%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C%22props%22%3A%7B%22initialPathName%22%3A%22%2F%22%2C%22referringDomain%22%3A%22%24direct%22%7D%7D%2C%22distinct_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22%24device_id%22%3A%**********-b42d-7ad0-a57b-91d7953c591b%22%2C%22%24user_state%22%3A%22identified%22%2C%22%24epp%22%3Atrue%2C%22%24stored_group_properties%22%3A%7B%22cluster%22%3A%7B%22app_pathname%22%3A%22welcome%22%2C%22app_theme%22%3A%22light%22%2C%22cluster_creation_date%22%3A%222025-06-10T10%3A16%3A06.500Z%22%2C%22nodes_count%22%3A3%2C%22traffic_last_month%22%3A0%2C%22input_traffic_last_month%22%3A0%2C%22users_count%22%3A0%2C%22license_count%22%3A0%2C%22node_leader_app_version%22%3A%226.1.12%2B23f653e%22%2C%22installation_source%22%3A%22docker%22%2C%22nodes%22%3A%7B%2277BkJC_dSf-fmslRiZeKaQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22RWbZVAVESbG-6WYuipatjQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22u8f0KQAgRmioRFol9sDOHg%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%7D%2C%22plugin_is_enterprise_plugin_installed%22%3Afalse%2C%22plugins%22%3A%5B%22Elasticsearch%207%20Support%3A6.1.12%2B23f653e%22%2C%22Threat%20Intelligence%20Plugin%3A6.1.12%2B23f653e%22%2C%22Integrations%3A6.1.12%2B23f653e%22%2C%22OpenSearch%202%20Support%3A6.1.12%2B23f653e%22%2C%22AWS%20plugins%3A6.1.12%2B23f653e%22%5D%2C%22search_cluster_nodes_count%22%3A3%2C%22search_cluster_version%22%3A%22DataNode%3A6.1.12%2B23f653e%22%2C%22data_nodes_count%22%3A3%2C%22cluster_id%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%2C%22user%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22user_is_local_admin%22%3Atrue%2C%22user_role_count%22%3A1%2C%22user_team_count%22%3A0%7D%7D%2C%22%24groups%22%3A%7B%22cluster%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%7D%2C%22%24capture_rate_limit%22%3A%7B%22tokens%22%3A99%2C%22last%22%3A1749551724300%7D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A9000%2Fwelcome%22%7D%2C%22%24user_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%7D
2025-06-11 18:33:03.600 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Extracted JWT token: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.600 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token found, length: 174
2025-06-11 18:33:03.600 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token first 20 chars: eyJhbGciOiJIUzUxMiJ9...
2025-06-11 18:33:03.600 [http-nio-8080-exec-9] DEBUG com.mazai.pos.security.jwt.JwtUtils - Validating JWT token, length: 174
2025-06-11 18:33:03.600 [http-nio-8080-exec-9] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT token first 20 chars: eyJhbGciOiJIUzUxMiJ9...
2025-06-11 18:33:03.600 [http-nio-8080-exec-9] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT structure appears valid (has 3 parts)
2025-06-11 18:33:03.600 [http-nio-8080-exec-9] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT header: {"alg":"HS512"}
2025-06-11 18:33:03.600 [http-nio-8080-exec-9] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT payload: {"sub":"admin","iat":1749637983,"exp":1749724383}
2025-06-11 18:33:03.600 [http-nio-8080-exec-9] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT expiration time: 1749724383, current time: 1749637983, diff: 86400 seconds
2025-06-11 18:33:03.602 [http-nio-8080-exec-9] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT token is valid
2025-06-11 18:33:03.602 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token valid: true
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /api/orders
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request headers:
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   host: localhost:8080
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   connection: keep-alive
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-platform: "Windows"
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept: application/json
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-mobile: ?0
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   origin: http://localhost:3000
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-site: same-site
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-mode: cors
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-dest: empty
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   referer: http://localhost:3000/
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-encoding: gzip, deflate, br, zstd
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-language: en-MY,en;q=0.9,zh-MY;q=0.8,zh;q=0.7,ms-MY;q=0.6,ms;q=0.5,en-US;q=0.4
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   cookie: ph_phc_t3lgBB66QsPW4HEfiGopO14um4XGNtBcefEKYWelWda_posthog=%7B%22%24sesid%22%3A%5B1749551724309%2C%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C1749551723559%5D%2C%22%24client_session_props%22%3A%7B%22sessionId%22%3A%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C%22props%22%3A%7B%22initialPathName%22%3A%22%2F%22%2C%22referringDomain%22%3A%22%24direct%22%7D%7D%2C%22distinct_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22%24device_id%22%3A%**********-b42d-7ad0-a57b-91d7953c591b%22%2C%22%24user_state%22%3A%22identified%22%2C%22%24epp%22%3Atrue%2C%22%24stored_group_properties%22%3A%7B%22cluster%22%3A%7B%22app_pathname%22%3A%22welcome%22%2C%22app_theme%22%3A%22light%22%2C%22cluster_creation_date%22%3A%222025-06-10T10%3A16%3A06.500Z%22%2C%22nodes_count%22%3A3%2C%22traffic_last_month%22%3A0%2C%22input_traffic_last_month%22%3A0%2C%22users_count%22%3A0%2C%22license_count%22%3A0%2C%22node_leader_app_version%22%3A%226.1.12%2B23f653e%22%2C%22installation_source%22%3A%22docker%22%2C%22nodes%22%3A%7B%2277BkJC_dSf-fmslRiZeKaQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22RWbZVAVESbG-6WYuipatjQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22u8f0KQAgRmioRFol9sDOHg%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%7D%2C%22plugin_is_enterprise_plugin_installed%22%3Afalse%2C%22plugins%22%3A%5B%22Elasticsearch%207%20Support%3A6.1.12%2B23f653e%22%2C%22Threat%20Intelligence%20Plugin%3A6.1.12%2B23f653e%22%2C%22Integrations%3A6.1.12%2B23f653e%22%2C%22OpenSearch%202%20Support%3A6.1.12%2B23f653e%22%2C%22AWS%20plugins%3A6.1.12%2B23f653e%22%5D%2C%22search_cluster_nodes_count%22%3A3%2C%22search_cluster_version%22%3A%22DataNode%3A6.1.12%2B23f653e%22%2C%22data_nodes_count%22%3A3%2C%22cluster_id%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%2C%22user%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22user_is_local_admin%22%3Atrue%2C%22user_role_count%22%3A1%2C%22user_team_count%22%3A0%7D%7D%2C%22%24groups%22%3A%7B%22cluster%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%7D%2C%22%24capture_rate_limit%22%3A%7B%22tokens%22%3A99%2C%22last%22%3A1749551724300%7D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A9000%2Fwelcome%22%7D%2C%22%24user_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%7D
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Authorization header: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request method: GET
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request URL: http://localhost:8080/api/orders
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - All request headers:
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   host: localhost:8080
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   connection: keep-alive
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-platform: "Windows"
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept: application/json
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-mobile: ?0
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   origin: http://localhost:3000
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-site: same-site
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-mode: cors
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-dest: empty
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   referer: http://localhost:3000/
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-encoding: gzip, deflate, br, zstd
2025-06-11 18:33:03.603 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-language: en-MY,en;q=0.9,zh-MY;q=0.8,zh;q=0.7,ms-MY;q=0.6,ms;q=0.5,en-US;q=0.4
2025-06-11 18:33:03.604 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   cookie: ph_phc_t3lgBB66QsPW4HEfiGopO14um4XGNtBcefEKYWelWda_posthog=%7B%22%24sesid%22%3A%5B1749551724309%2C%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C1749551723559%5D%2C%22%24client_session_props%22%3A%7B%22sessionId%22%3A%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C%22props%22%3A%7B%22initialPathName%22%3A%22%2F%22%2C%22referringDomain%22%3A%22%24direct%22%7D%7D%2C%22distinct_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22%24device_id%22%3A%**********-b42d-7ad0-a57b-91d7953c591b%22%2C%22%24user_state%22%3A%22identified%22%2C%22%24epp%22%3Atrue%2C%22%24stored_group_properties%22%3A%7B%22cluster%22%3A%7B%22app_pathname%22%3A%22welcome%22%2C%22app_theme%22%3A%22light%22%2C%22cluster_creation_date%22%3A%222025-06-10T10%3A16%3A06.500Z%22%2C%22nodes_count%22%3A3%2C%22traffic_last_month%22%3A0%2C%22input_traffic_last_month%22%3A0%2C%22users_count%22%3A0%2C%22license_count%22%3A0%2C%22node_leader_app_version%22%3A%226.1.12%2B23f653e%22%2C%22installation_source%22%3A%22docker%22%2C%22nodes%22%3A%7B%2277BkJC_dSf-fmslRiZeKaQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22RWbZVAVESbG-6WYuipatjQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22u8f0KQAgRmioRFol9sDOHg%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%7D%2C%22plugin_is_enterprise_plugin_installed%22%3Afalse%2C%22plugins%22%3A%5B%22Elasticsearch%207%20Support%3A6.1.12%2B23f653e%22%2C%22Threat%20Intelligence%20Plugin%3A6.1.12%2B23f653e%22%2C%22Integrations%3A6.1.12%2B23f653e%22%2C%22OpenSearch%202%20Support%3A6.1.12%2B23f653e%22%2C%22AWS%20plugins%3A6.1.12%2B23f653e%22%5D%2C%22search_cluster_nodes_count%22%3A3%2C%22search_cluster_version%22%3A%22DataNode%3A6.1.12%2B23f653e%22%2C%22data_nodes_count%22%3A3%2C%22cluster_id%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%2C%22user%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22user_is_local_admin%22%3Atrue%2C%22user_role_count%22%3A1%2C%22user_team_count%22%3A0%7D%7D%2C%22%24groups%22%3A%7B%22cluster%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%7D%2C%22%24capture_rate_limit%22%3A%7B%22tokens%22%3A99%2C%22last%22%3A1749551724300%7D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A9000%2Fwelcome%22%7D%2C%22%24user_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%7D
2025-06-11 18:33:03.604 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Extracted JWT token: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.604 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token found, length: 174
2025-06-11 18:33:03.604 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token first 20 chars: eyJhbGciOiJIUzUxMiJ9...
2025-06-11 18:33:03.604 [http-nio-8080-exec-1] DEBUG com.mazai.pos.security.jwt.JwtUtils - Validating JWT token, length: 174
2025-06-11 18:33:03.604 [http-nio-8080-exec-1] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT token first 20 chars: eyJhbGciOiJIUzUxMiJ9...
2025-06-11 18:33:03.604 [http-nio-8080-exec-1] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT structure appears valid (has 3 parts)
2025-06-11 18:33:03.604 [http-nio-8080-exec-1] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT header: {"alg":"HS512"}
2025-06-11 18:33:03.604 [http-nio-8080-exec-1] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT payload: {"sub":"admin","iat":1749637983,"exp":1749724383}
2025-06-11 18:33:03.604 [http-nio-8080-exec-1] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT expiration time: 1749724383, current time: 1749637983, diff: 86400 seconds
2025-06-11 18:33:03.604 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Username from JWT: admin
2025-06-11 18:33:03.606 [http-nio-8080-exec-1] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT token is valid
2025-06-11 18:33:03.606 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token valid: true
2025-06-11 18:33:03.608 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Username from JWT: admin
2025-06-11 18:33:03.608 [http-nio-8080-exec-9] DEBUG c.m.p.s.s.UserDetailsServiceImpl - Loading user details for username: admin
2025-06-11 18:33:03.612 [http-nio-8080-exec-9] DEBUG c.m.p.s.s.UserDetailsServiceImpl - User found: id=4, username=admin, role=ADMIN
2025-06-11 18:33:03.612 [http-nio-8080-exec-9] DEBUG c.m.p.s.s.UserDetailsServiceImpl - UserDetails built successfully for username: admin
2025-06-11 18:33:03.613 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - User details loaded for username: admin
2025-06-11 18:33:03.613 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - User authorities: [ADMIN]
2025-06-11 18:33:03.613 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Authentication set in SecurityContext for user: admin
2025-06-11 18:33:03.614 [http-nio-8080-exec-1] DEBUG c.m.p.s.s.UserDetailsServiceImpl - Loading user details for username: admin
2025-06-11 18:33:03.617 [http-nio-8080-exec-1] DEBUG c.m.p.s.s.UserDetailsServiceImpl - User found: id=4, username=admin, role=ADMIN
2025-06-11 18:33:03.617 [http-nio-8080-exec-1] DEBUG c.m.p.s.s.UserDetailsServiceImpl - UserDetails built successfully for username: admin
2025-06-11 18:33:03.618 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - User details loaded for username: admin
2025-06-11 18:33:03.618 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - User authorities: [ADMIN]
2025-06-11 18:33:03.618 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Authentication set in SecurityContext for user: admin
2025-06-11 18:33:03.823 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /api/orders
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request headers:
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   host: localhost:8080
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   connection: keep-alive
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-platform: "Windows"
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept: application/json
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-mobile: ?0
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   origin: http://localhost:3000
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-site: same-site
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-mode: cors
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-dest: empty
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   referer: http://localhost:3000/
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-encoding: gzip, deflate, br, zstd
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-language: en-MY,en;q=0.9,zh-MY;q=0.8,zh;q=0.7,ms-MY;q=0.6,ms;q=0.5,en-US;q=0.4
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   cookie: ph_phc_t3lgBB66QsPW4HEfiGopO14um4XGNtBcefEKYWelWda_posthog=%7B%22%24sesid%22%3A%5B1749551724309%2C%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C1749551723559%5D%2C%22%24client_session_props%22%3A%7B%22sessionId%22%3A%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C%22props%22%3A%7B%22initialPathName%22%3A%22%2F%22%2C%22referringDomain%22%3A%22%24direct%22%7D%7D%2C%22distinct_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22%24device_id%22%3A%**********-b42d-7ad0-a57b-91d7953c591b%22%2C%22%24user_state%22%3A%22identified%22%2C%22%24epp%22%3Atrue%2C%22%24stored_group_properties%22%3A%7B%22cluster%22%3A%7B%22app_pathname%22%3A%22welcome%22%2C%22app_theme%22%3A%22light%22%2C%22cluster_creation_date%22%3A%222025-06-10T10%3A16%3A06.500Z%22%2C%22nodes_count%22%3A3%2C%22traffic_last_month%22%3A0%2C%22input_traffic_last_month%22%3A0%2C%22users_count%22%3A0%2C%22license_count%22%3A0%2C%22node_leader_app_version%22%3A%226.1.12%2B23f653e%22%2C%22installation_source%22%3A%22docker%22%2C%22nodes%22%3A%7B%2277BkJC_dSf-fmslRiZeKaQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22RWbZVAVESbG-6WYuipatjQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22u8f0KQAgRmioRFol9sDOHg%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%7D%2C%22plugin_is_enterprise_plugin_installed%22%3Afalse%2C%22plugins%22%3A%5B%22Elasticsearch%207%20Support%3A6.1.12%2B23f653e%22%2C%22Threat%20Intelligence%20Plugin%3A6.1.12%2B23f653e%22%2C%22Integrations%3A6.1.12%2B23f653e%22%2C%22OpenSearch%202%20Support%3A6.1.12%2B23f653e%22%2C%22AWS%20plugins%3A6.1.12%2B23f653e%22%5D%2C%22search_cluster_nodes_count%22%3A3%2C%22search_cluster_version%22%3A%22DataNode%3A6.1.12%2B23f653e%22%2C%22data_nodes_count%22%3A3%2C%22cluster_id%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%2C%22user%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22user_is_local_admin%22%3Atrue%2C%22user_role_count%22%3A1%2C%22user_team_count%22%3A0%7D%7D%2C%22%24groups%22%3A%7B%22cluster%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%7D%2C%22%24capture_rate_limit%22%3A%7B%22tokens%22%3A99%2C%22last%22%3A1749551724300%7D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A9000%2Fwelcome%22%7D%2C%22%24user_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%7D
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Authorization header: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request method: GET
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Request URL: http://localhost:8080/api/orders
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - All request headers:
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   host: localhost:8080
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   connection: keep-alive
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-platform: "Windows"
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept: application/json
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-ch-ua-mobile: ?0
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   origin: http://localhost:3000
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-site: same-site
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-mode: cors
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   sec-fetch-dest: empty
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   referer: http://localhost:3000/
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-encoding: gzip, deflate, br, zstd
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   accept-language: en-MY,en;q=0.9,zh-MY;q=0.8,zh;q=0.7,ms-MY;q=0.6,ms;q=0.5,en-US;q=0.4
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter -   cookie: ph_phc_t3lgBB66QsPW4HEfiGopO14um4XGNtBcefEKYWelWda_posthog=%7B%22%24sesid%22%3A%5B1749551724309%2C%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C1749551723559%5D%2C%22%24client_session_props%22%3A%7B%22sessionId%22%3A%**********-b427-7124-bb0a-3dbd02a75d8b%22%2C%22props%22%3A%7B%22initialPathName%22%3A%22%2F%22%2C%22referringDomain%22%3A%22%24direct%22%7D%7D%2C%22distinct_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22%24device_id%22%3A%**********-b42d-7ad0-a57b-91d7953c591b%22%2C%22%24user_state%22%3A%22identified%22%2C%22%24epp%22%3Atrue%2C%22%24stored_group_properties%22%3A%7B%22cluster%22%3A%7B%22app_pathname%22%3A%22welcome%22%2C%22app_theme%22%3A%22light%22%2C%22cluster_creation_date%22%3A%222025-06-10T10%3A16%3A06.500Z%22%2C%22nodes_count%22%3A3%2C%22traffic_last_month%22%3A0%2C%22input_traffic_last_month%22%3A0%2C%22users_count%22%3A0%2C%22license_count%22%3A0%2C%22node_leader_app_version%22%3A%226.1.12%2B23f653e%22%2C%22installation_source%22%3A%22docker%22%2C%22nodes%22%3A%7B%2277BkJC_dSf-fmslRiZeKaQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22RWbZVAVESbG-6WYuipatjQ%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%2C%22u8f0KQAgRmioRFol9sDOHg%22%3A%7B%22version%22%3A%222.15.0%22%2C%22os%22%3A%7B%22refresh_interval_in_millis%22%3A1000%2C%22name%22%3A%22Linux%22%2C%22pretty_name%22%3A%22Ubuntu%2022.04.5%20LTS%22%2C%22arch%22%3A%22amd64%22%2C%22version%22%3A%**********-microsoft-standard-WSL2%22%2C%22available_processors%22%3A16%2C%22allocated_processors%22%3A16%7D%2C%22jvm_mem_heap_max_in_bytes%22%3A1073741824%2C%22roles%22%3A%5B%22cluster_manager%22%2C%22data%22%2C%22ingest%22%2C%22remote_cluster_client%22%5D%7D%7D%2C%22plugin_is_enterprise_plugin_installed%22%3Afalse%2C%22plugins%22%3A%5B%22Elasticsearch%207%20Support%3A6.1.12%2B23f653e%22%2C%22Threat%20Intelligence%20Plugin%3A6.1.12%2B23f653e%22%2C%22Integrations%3A6.1.12%2B23f653e%22%2C%22OpenSearch%202%20Support%3A6.1.12%2B23f653e%22%2C%22AWS%20plugins%3A6.1.12%2B23f653e%22%5D%2C%22search_cluster_nodes_count%22%3A3%2C%22search_cluster_version%22%3A%22DataNode%3A6.1.12%2B23f653e%22%2C%22data_nodes_count%22%3A3%2C%22cluster_id%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%2C%22user%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%2C%22user_is_local_admin%22%3Atrue%2C%22user_role_count%22%3A1%2C%22user_team_count%22%3A0%7D%7D%2C%22%24groups%22%3A%7B%22cluster%22%3A%22e0c2c6e2-5d44-4fab-9b4d-53b053d1fd7e%22%7D%2C%22%24capture_rate_limit%22%3A%7B%22tokens%22%3A99%2C%22last%22%3A1749551724300%7D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A9000%2Fwelcome%22%7D%2C%22%24user_id%22%3A%22f573be02bc163bc2381212b4213d2f667ca3e2ff4366037aefceccaff92701bd%22%7D
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Extracted JWT token: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0OTYzNzk4MywiZXhwIjoxNzQ5NzI0MzgzfQ.uO1Ow7fDSWhgwIfndJBg0W7ILddMaBjyKLE9yk5FClwvnf85Ko2IGVXpzhZ8ZEsVmpKqW9h1mDULs7PjMeDuWw
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token found, length: 174
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token first 20 chars: eyJhbGciOiJIUzUxMiJ9...
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG com.mazai.pos.security.jwt.JwtUtils - Validating JWT token, length: 174
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT token first 20 chars: eyJhbGciOiJIUzUxMiJ9...
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT structure appears valid (has 3 parts)
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT header: {"alg":"HS512"}
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT payload: {"sub":"admin","iat":1749637983,"exp":1749724383}
2025-06-11 18:33:03.824 [http-nio-8080-exec-2] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT expiration time: 1749724383, current time: 1749637983, diff: 86400 seconds
2025-06-11 18:33:03.827 [http-nio-8080-exec-2] DEBUG com.mazai.pos.security.jwt.JwtUtils - JWT token is valid
2025-06-11 18:33:03.827 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - JWT token valid: true
2025-06-11 18:33:03.829 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Username from JWT: admin
2025-06-11 18:33:03.833 [http-nio-8080-exec-2] DEBUG c.m.p.s.s.UserDetailsServiceImpl - Loading user details for username: admin
2025-06-11 18:33:03.837 [http-nio-8080-exec-2] DEBUG c.m.p.s.s.UserDetailsServiceImpl - User found: id=4, username=admin, role=ADMIN
2025-06-11 18:33:03.837 [http-nio-8080-exec-2] DEBUG c.m.p.s.s.UserDetailsServiceImpl - UserDetails built successfully for username: admin
2025-06-11 18:33:03.839 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - User details loaded for username: admin
2025-06-11 18:33:03.839 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - User authorities: [ADMIN]
2025-06-11 18:33:03.839 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Authentication set in SecurityContext for user: admin
2025-06-11 18:33:11.027 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/health
2025-06-11 18:33:11.027 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/health
2025-06-11 18:33:11.028 [http-nio-8080-exec-3] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-11 18:33:11.028 [http-nio-8080-exec-3] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749637991028}
2025-06-11 18:33:11.996 [http-nio-8080-exec-4] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:11.996 [http-nio-8080-exec-4] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:13.479 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/products
2025-06-11 18:33:13.479 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/products
2025-06-11 18:33:13.486 [http-nio-8080-exec-6] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:13.487 [http-nio-8080-exec-6] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:14.296 [http-nio-8080-exec-7] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:14.296 [http-nio-8080-exec-7] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:15.005 [http-nio-8080-exec-10] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/products
2025-06-11 18:33:15.005 [http-nio-8080-exec-10] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/products
2025-06-11 18:33:15.007 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:15.007 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:21.667 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/products
2025-06-11 18:33:21.667 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:21.667 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/products
2025-06-11 18:33:21.667 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:22.197 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:22.197 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:22.201 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:22.201 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:23.513 [http-nio-8080-exec-4] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/products
2025-06-11 18:33:23.513 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:23.513 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:23.513 [http-nio-8080-exec-4] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/products
2025-06-11 18:33:24.359 [http-nio-8080-exec-6] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:24.359 [http-nio-8080-exec-6] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:28.223 [http-nio-8080-exec-7] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/products
2025-06-11 18:33:28.223 [http-nio-8080-exec-10] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:28.224 [http-nio-8080-exec-10] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:28.224 [http-nio-8080-exec-7] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/products
2025-06-11 18:33:28.862 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:28.862 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:28.863 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:28.863 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:29.598 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:29.598 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:29.601 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:29.601 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:30.363 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/products
2025-06-11 18:33:30.363 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/products
2025-06-11 18:33:30.363 [http-nio-8080-exec-4] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:30.363 [http-nio-8080-exec-4] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:34.372 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/product-variants/product/46
2025-06-11 18:33:34.372 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/product-variants/product/46
2025-06-11 18:33:43.406 [http-nio-8080-exec-6] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/product-variants/product/16
2025-06-11 18:33:43.407 [http-nio-8080-exec-6] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/product-variants/product/16
2025-06-11 18:33:46.066 [http-nio-8080-exec-7] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: POST /mobile/orders
2025-06-11 18:33:46.066 [http-nio-8080-exec-7] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders
2025-06-11 18:33:46.079 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileOrderController - Creating order with 2 items
2025-06-11 18:33:46.169 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileOrderController - Order #161 queued for kitchen printing
2025-06-11 18:33:46.169 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileOrderController - Order created successfully with ID: 161
2025-06-11 18:33:46.173 [pool-2-thread-1] INFO  c.m.pos.service.MultiPrinterService - 使用printer3打印厨房订单 - 订单 #161, 包含1个商品 (类型: NEW)
2025-06-11 18:33:46.173 [pool-2-thread-1] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 开始使用printer3打印厨房订单 #161 (类型: NEW)
2025-06-11 18:33:46.246 [http-nio-8080-exec-10] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:46.246 [http-nio-8080-exec-10] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:47.363 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/today
2025-06-11 18:33:47.363 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/today
2025-06-11 18:33:49.959 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:49.959 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:49.959 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:49.959 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:50.803 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/today
2025-06-11 18:33:50.803 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/today
2025-06-11 18:33:51.203 [pool-2-thread-1] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 发送打印命令失败 *************:9100 - Connect timed out
java.net.SocketTimeoutException: Connect timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:546)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at com.mazai.pos.service.impl.EscPosPrinterServiceImpl.sendToPrinterWithConfig(EscPosPrinterServiceImpl.java:704)
	at com.mazai.pos.service.impl.EscPosPrinterServiceImpl.printKitchenOrderWithPrinterAndType(EscPosPrinterServiceImpl.java:668)
	at com.mazai.pos.service.MultiPrinterService.printKitchenOrdersWithType(MultiPrinterService.java:120)
	at com.mazai.pos.service.impl.PrintQueueServiceImpl.lambda$0(PrintQueueServiceImpl.java:128)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-11 18:33:51.224 [pool-2-thread-1] ERROR c.m.pos.service.MultiPrinterService - printer3打印厨房订单失败
2025-06-11 18:33:51.224 [pool-2-thread-1] INFO  c.m.pos.service.MultiPrinterService - 使用printer1打印厨房订单 - 订单 #161, 包含1个商品 (类型: NEW)
2025-06-11 18:33:51.225 [pool-2-thread-1] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 开始使用printer1打印厨房订单 #161 (类型: NEW)
2025-06-11 18:33:51.772 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/today
2025-06-11 18:33:51.772 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/today
2025-06-11 18:33:52.524 [http-nio-8080-exec-4] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/products
2025-06-11 18:33:52.524 [http-nio-8080-exec-4] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/products
2025-06-11 18:33:52.524 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:52.524 [http-nio-8080-exec-5] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:53.832 [http-nio-8080-exec-6] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:53.832 [http-nio-8080-exec-6] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:53.838 [http-nio-8080-exec-7] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:33:53.838 [http-nio-8080-exec-7] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:33:56.238 [pool-2-thread-1] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 发送打印命令失败 *************:9100 - Connect timed out
java.net.SocketTimeoutException: Connect timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:546)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at com.mazai.pos.service.impl.EscPosPrinterServiceImpl.sendToPrinterWithConfig(EscPosPrinterServiceImpl.java:704)
	at com.mazai.pos.service.impl.EscPosPrinterServiceImpl.printKitchenOrderWithPrinterAndType(EscPosPrinterServiceImpl.java:668)
	at com.mazai.pos.service.MultiPrinterService.printKitchenOrdersWithType(MultiPrinterService.java:120)
	at com.mazai.pos.service.impl.PrintQueueServiceImpl.lambda$0(PrintQueueServiceImpl.java:128)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-11 18:33:56.239 [pool-2-thread-1] ERROR c.m.pos.service.MultiPrinterService - printer1打印厨房订单失败
2025-06-11 18:33:58.155 [http-nio-8080-exec-10] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/today
2025-06-11 18:33:58.155 [http-nio-8080-exec-10] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/today
2025-06-11 18:34:01.194 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:34:01.194 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:34:01.194 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:34:01.194 [http-nio-8080-exec-8] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:34:02.181 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:34:02.181 [http-nio-8080-exec-9] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:34:02.182 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:34:02.182 [http-nio-8080-exec-2] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:34:03.133 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:34:03.133 [http-nio-8080-exec-3] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:34:14.300 [http-nio-8080-exec-10] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: POST /mobile/payments/order-items
2025-06-11 18:34:14.300 [http-nio-8080-exec-10] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/payments/order-items
2025-06-11 18:34:14.361 [pool-2-thread-1] INFO  c.m.pos.service.MultiPrinterService - 使用打印机1打印收据 - 订单 #161 (收据类型: CHINESE)
2025-06-11 18:34:14.361 [http-nio-8080-exec-10] INFO  c.m.p.c.MobilePaymentController - Order items payment order queued for printing (receiptType: CHINESE)
2025-06-11 18:34:14.361 [pool-2-thread-1] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 开始使用printer1打印订单 #161 (收据类型: CHINESE)
2025-06-11 18:34:14.504 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Processing request: GET /mobile/orders/status/OPEN
2025-06-11 18:34:14.504 [http-nio-8080-exec-1] DEBUG c.m.pos.security.jwt.AuthTokenFilter - Skipping JWT authentication for mobile API: /mobile/orders/status/OPEN
2025-06-11 18:34:19.416 [pool-2-thread-1] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 发送打印命令失败 *************:9100 - Connect timed out
java.net.SocketTimeoutException: Connect timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:546)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at com.mazai.pos.service.impl.EscPosPrinterServiceImpl.sendToPrinterWithConfig(EscPosPrinterServiceImpl.java:704)
	at com.mazai.pos.service.impl.EscPosPrinterServiceImpl.printOrderWithPrinterAndReceiptType(EscPosPrinterServiceImpl.java:470)
	at com.mazai.pos.service.MultiPrinterService.printReceiptWithType(MultiPrinterService.java:68)
	at com.mazai.pos.service.impl.PrintQueueServiceImpl.lambda$0(PrintQueueServiceImpl.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-11 18:46:01.200 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-11 18:46:01.205 [Thread-5] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-11 18:46:01.206 [tomcat-shutdown] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-06-11 18:46:01.220 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-11 18:46:01.221 [Thread-5] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-06-11 18:46:01.922 [Thread-5] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 18:46:01.922 [Thread-5] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 18:46:01.925 [Thread-5] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 18:46:02.118 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 5980 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 18:46:02.118 [restartedMain] DEBUG com.mazai.pos.PosApplication - Running with Spring Boot v3.4.5, Spring v6.2.6
2025-06-11 18:46:02.118 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 18:46:02.634 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 18:46:02.688 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 53 ms. Found 7 JPA repository interfaces.
2025-06-11 18:46:02.995 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-11 18:46:02.998 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-11 18:46:02.998 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 18:46:02.998 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.40]
2025-06-11 18:46:03.026 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 18:46:03.027 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 903 ms
2025-06-11 18:46:03.088 [restartedMain] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 18:46:03.093 [restartedMain] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-11 18:46:03.115 [restartedMain] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-11 18:46:03.122 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 18:46:03.123 [restartedMain] INFO  o.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'org.springframework.jdbc.datasource.DriverManagerDataSource@4503d54f']
	Database driver: undefined/unknown
	Database version: 8.0.41
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-11 18:46:03.441 [restartedMain] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-11 18:46:03.505 [restartedMain] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 18:46:03.691 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Global AuthenticationManager configured with AuthenticationProvider bean with name authenticationProvider
2025-06-11 18:46:03.692 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 18:46:03.902 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 18:46:08.910 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 18:46:08.910 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 18:46:08.986 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 18:46:09.361 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-11 18:46:09.370 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-11 18:46:09.374 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-06-11 18:46:09.377 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 7.364 seconds (process running for 3403.814)
2025-06-11 18:46:09.379 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-06-11 18:46:17.927 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-11 18:46:17.928 [Thread-7] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-11 18:46:17.929 [tomcat-shutdown] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-06-11 18:46:17.936 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-11 18:46:17.936 [Thread-7] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-06-11 18:46:17.955 [Thread-7] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 18:46:17.955 [Thread-7] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 18:46:17.956 [Thread-7] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 18:46:18.069 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 5980 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 18:46:18.069 [restartedMain] DEBUG com.mazai.pos.PosApplication - Running with Spring Boot v3.4.5, Spring v6.2.6
2025-06-11 18:46:18.069 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 18:46:18.351 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 18:46:18.400 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 48 ms. Found 7 JPA repository interfaces.
2025-06-11 18:46:18.551 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-11 18:46:18.581 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 509 ms
2025-06-11 18:46:18.633 [restartedMain] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-11 18:46:18.638 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 18:46:18.804 [restartedMain] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 18:46:18.911 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Global AuthenticationManager configured with AuthenticationProvider bean with name authenticationProvider
2025-06-11 18:46:18.911 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 18:46:19.053 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 18:46:24.060 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 18:46:24.060 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 18:46:24.113 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 18:46:24.387 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-11 18:46:24.398 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-06-11 18:46:24.402 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.373 seconds (process running for 3418.838)
2025-06-11 18:46:24.404 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-06-11 18:46:28.389 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 18:46:28.390 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-11 18:46:33.993 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-11 18:46:33.994 [Thread-11] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-11 18:46:34.000 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-11 18:46:34.114 [Thread-11] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 18:46:34.114 [Thread-11] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 18:46:34.115 [Thread-11] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 18:46:34.228 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 5980 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 18:46:34.229 [restartedMain] DEBUG com.mazai.pos.PosApplication - Running with Spring Boot v3.4.5, Spring v6.2.6
2025-06-11 18:46:34.229 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 18:46:34.495 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 18:46:34.524 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 7 JPA repository interfaces.
2025-06-11 18:46:34.693 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-11 18:46:34.713 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 482 ms
2025-06-11 18:46:34.761 [restartedMain] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-11 18:46:34.767 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 18:46:34.929 [restartedMain] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 18:46:35.035 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Global AuthenticationManager configured with AuthenticationProvider bean with name authenticationProvider
2025-06-11 18:46:35.036 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 18:46:35.178 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 18:46:40.185 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 18:46:40.185 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 18:46:40.238 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 18:46:40.517 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-11 18:46:40.530 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-06-11 18:46:40.534 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.372 seconds (process running for 3434.971)
2025-06-11 18:46:40.535 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-06-11 18:46:43.254 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 18:46:43.255 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-11 18:46:52.152 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-11 18:46:52.153 [Thread-15] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-11 18:46:52.160 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-11 18:46:52.274 [Thread-15] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 18:46:52.274 [Thread-15] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 18:46:52.275 [Thread-15] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 18:46:52.394 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 5980 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 18:46:52.394 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 18:46:52.882 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 18:46:53.128 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 18:46:53.251 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 18:46:58.254 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 18:46:58.254 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 18:46:58.302 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 18:46:58.557 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.233 seconds (process running for 3452.993)
2025-06-11 19:06:31.831 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileOrderController - Creating order with 1 items
2025-06-11 19:06:31.870 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileOrderController - Order #162 queued for kitchen printing
2025-06-11 19:06:31.870 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileOrderController - Order created successfully with ID: 162
2025-06-11 19:06:31.871 [pool-6-thread-1] INFO  c.m.pos.service.MultiPrinterService - 使用printer1打印厨房订单 - 订单 #162, 包含1个商品 (类型: NEW)
2025-06-11 19:06:31.871 [pool-6-thread-1] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 开始使用printer1打印厨房订单 #162 (类型: NEW)
2025-06-11 19:06:36.877 [pool-6-thread-1] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 发送打印命令失败 *************:9100 - Connect timed out
java.net.SocketTimeoutException: Connect timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:546)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at com.mazai.pos.service.impl.EscPosPrinterServiceImpl.sendToPrinterWithConfig(EscPosPrinterServiceImpl.java:704)
	at com.mazai.pos.service.impl.EscPosPrinterServiceImpl.printKitchenOrderWithPrinterAndType(EscPosPrinterServiceImpl.java:668)
	at com.mazai.pos.service.MultiPrinterService.printKitchenOrdersWithType(MultiPrinterService.java:120)
	at com.mazai.pos.service.impl.PrintQueueServiceImpl.lambda$0(PrintQueueServiceImpl.java:128)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-11 19:06:36.877 [pool-6-thread-1] ERROR c.m.pos.service.MultiPrinterService - printer1打印厨房订单失败
2025-06-11 19:10:44.595 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:10:44.597 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:10:47.873 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:10:49.533 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:10:49.733 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 19:10:54.739 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 19:10:54.740 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 19:10:54.841 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:10:55.543 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 11.593 seconds (process running for 12.152)
2025-06-11 19:12:10.400 [Thread-5] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 19:12:10.401 [Thread-5] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 19:12:10.528 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:12:10.528 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:12:11.164 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:12:11.498 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:12:11.672 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 19:12:16.678 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 19:12:16.679 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 19:12:16.781 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:12:17.132 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.668 seconds (process running for 93.741)
2025-06-11 19:12:25.778 [Thread-7] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 19:12:25.778 [Thread-7] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 19:12:26.025 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:12:26.025 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:12:27.153 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:12:27.867 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:12:28.181 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 19:12:33.192 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 19:12:33.192 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 19:12:33.261 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:12:33.562 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 7.669 seconds (process running for 110.172)
2025-06-11 19:12:47.298 [Thread-11] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 19:12:47.298 [Thread-11] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 19:12:47.393 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:12:47.393 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:12:48.164 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:12:48.510 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:12:48.693 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 19:12:53.699 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 19:12:53.699 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 19:12:53.754 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:12:54.060 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.711 seconds (process running for 130.669)
2025-06-11 19:13:04.761 [Thread-15] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 19:13:04.761 [Thread-15] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 19:13:04.843 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:13:04.843 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:13:05.371 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:13:05.689 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:13:05.821 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 19:13:10.837 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 19:13:10.837 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 19:13:10.886 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:13:11.146 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.341 seconds (process running for 147.754)
2025-06-11 19:13:18.880 [Thread-19] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 19:13:18.881 [Thread-19] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 19:13:18.976 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:13:18.977 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:13:19.405 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:13:19.769 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:13:19.943 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 19:13:24.956 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 19:13:24.957 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 19:13:25.004 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:13:25.278 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.342 seconds (process running for 161.888)
2025-06-11 19:13:33.007 [Thread-23] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 19:13:33.008 [Thread-23] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 19:13:33.102 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:13:33.102 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:13:33.529 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:13:33.879 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:13:34.047 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 19:13:39.051 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 19:13:39.052 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 19:13:39.098 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:13:39.363 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.297 seconds (process running for 175.973)
2025-06-11 19:13:56.190 [Thread-27] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 19:13:56.191 [Thread-27] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 19:13:56.288 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:13:56.290 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:13:56.865 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:13:57.208 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:13:57.352 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 19:14:02.358 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 19:14:02.358 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 19:14:02.409 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:14:02.684 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.439 seconds (process running for 199.293)
2025-06-11 19:14:18.464 [Thread-31] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 19:14:18.464 [Thread-31] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 19:14:18.553 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:14:18.553 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:14:19.179 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:14:19.547 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:14:19.724 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 19:14:24.741 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 19:14:24.741 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 19:14:24.793 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:14:25.075 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.558 seconds (process running for 221.685)
2025-06-11 19:15:10.298 [http-nio-8080-exec-5] INFO  c.m.p.c.MobileOrderController - Creating order with 1 items
2025-06-11 19:15:10.398 [http-nio-8080-exec-5] INFO  c.m.p.c.MobileOrderController - Order #163 queued for kitchen printing
2025-06-11 19:15:10.398 [http-nio-8080-exec-5] INFO  c.m.p.c.MobileOrderController - Order created successfully with ID: 163
2025-06-11 19:15:10.399 [pool-10-thread-1] INFO  c.m.pos.service.MultiPrinterService - 使用printer1打印厨房订单 - 订单 #163, 包含1个商品 (类型: NEW)
2025-06-11 19:15:10.399 [pool-10-thread-1] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 开始使用printer1打印厨房订单 #163 (类型: NEW)
2025-06-11 19:15:15.418 [pool-10-thread-1] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 发送打印命令失败 *************:9100 - Connect timed out
java.net.SocketTimeoutException: Connect timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:546)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at com.mazai.pos.service.impl.EscPosPrinterServiceImpl.sendToPrinterWithConfig(EscPosPrinterServiceImpl.java:704)
	at com.mazai.pos.service.impl.EscPosPrinterServiceImpl.printKitchenOrderWithPrinterAndType(EscPosPrinterServiceImpl.java:668)
	at com.mazai.pos.service.MultiPrinterService.printKitchenOrdersWithType(MultiPrinterService.java:120)
	at com.mazai.pos.service.impl.PrintQueueServiceImpl.lambda$0(PrintQueueServiceImpl.java:128)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-11 19:15:15.434 [pool-10-thread-1] ERROR c.m.pos.service.MultiPrinterService - printer1打印厨房订单失败
2025-06-11 19:15:15.437 [Thread-35] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 19:15:15.437 [Thread-35] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 19:15:15.514 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:15:15.514 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:15:15.923 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:15:16.180 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:15:16.301 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 19:15:21.306 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 19:15:21.306 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 19:15:21.359 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:15:21.684 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.201 seconds (process running for 278.294)
2025-06-11 19:16:52.134 [Thread-39] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 19:16:52.134 [Thread-39] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 19:16:52.258 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:16:52.260 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:16:52.908 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:16:53.331 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:16:53.375 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Could not resolve attribute 'orderDate' of 'com.mazai.pos.model.Order'
2025-06-11 19:16:53.436 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Could not resolve attribute 'orderDate' of 'com.mazai.pos.model.Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.mazai.pos.PosApplication.main(PosApplication.java:14)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Could not resolve attribute 'orderDate' of 'com.mazai.pos.model.Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Could not resolve attribute 'orderDate' of 'com.mazai.pos.model.Order'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 38 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Could not resolve attribute 'orderDate' of 'com.mazai.pos.model.Order'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:120)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:104)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:431)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$4(RepositoryFactoryBeanSupport.java:350)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:356)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1865)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1814)
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Could not resolve attribute 'orderDate' of 'com.mazai.pos.model.Order'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:107)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:128)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:260)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:116)
	... 59 common frames omitted
Caused by: org.hibernate.query.sqm.PathElementException: Could not resolve attribute 'orderDate' of 'com.mazai.pos.model.Order'
	at org.hibernate.query.sqm.SqmPathSource.getSubPathSource(SqmPathSource.java:95)
	at org.hibernate.query.sqm.tree.domain.AbstractSqmPath.get(AbstractSqmPath.java:198)
	at org.hibernate.query.sqm.tree.domain.AbstractSqmPath.get(AbstractSqmPath.java:44)
	at org.springframework.data.jpa.repository.query.QueryUtils.getModelForPath(QueryUtils.java:989)
	at org.springframework.data.jpa.repository.query.QueryUtils.requiresOuterJoin(QueryUtils.java:839)
	at org.springframework.data.jpa.repository.query.QueryUtils.toExpressionRecursively(QueryUtils.java:794)
	at org.springframework.data.jpa.repository.query.QueryUtils.toExpressionRecursively(QueryUtils.java:773)
	at org.springframework.data.jpa.repository.query.QueryUtils.toExpressionRecursively(QueryUtils.java:769)
	at org.springframework.data.jpa.repository.query.JpaQueryCreator$PredicateBuilder.getTypedPath(JpaQueryCreator.java:387)
	at org.springframework.data.jpa.repository.query.JpaQueryCreator$PredicateBuilder.getComparablePath(JpaQueryCreator.java:383)
	at org.springframework.data.jpa.repository.query.JpaQueryCreator$PredicateBuilder.build(JpaQueryCreator.java:253)
	at org.springframework.data.jpa.repository.query.JpaQueryCreator.toPredicate(JpaQueryCreator.java:210)
	at org.springframework.data.jpa.repository.query.JpaQueryCreator.create(JpaQueryCreator.java:121)
	at org.springframework.data.jpa.repository.query.JpaQueryCreator.create(JpaQueryCreator.java:1)
	at org.springframework.data.repository.query.parser.AbstractQueryCreator.createCriteria(AbstractQueryCreator.java:119)
	at org.springframework.data.repository.query.parser.AbstractQueryCreator.createQuery(AbstractQueryCreator.java:95)
	at org.springframework.data.repository.query.parser.AbstractQueryCreator.createQuery(AbstractQueryCreator.java:81)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery$QueryPreparer.<init>(PartTreeJpaQuery.java:224)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery$CountQueryPreparer.<init>(PartTreeJpaQuery.java:370)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:103)
	... 63 common frames omitted
2025-06-11 19:17:07.021 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:17:07.022 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:17:07.927 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:17:08.320 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:17:08.358 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
2025-06-11 19:17:08.364 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.mazai.pos.PosApplication.main(PosApplication.java:14)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 38 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:120)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:104)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:431)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$4(RepositoryFactoryBeanSupport.java:350)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:356)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1865)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1814)
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:107)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:128)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:260)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:116)
	... 59 common frames omitted
Caused by: org.springframework.data.mapping.PropertyReferenceException: No property 'orderDate' found for type 'Order'
	at org.springframework.data.mapping.PropertyPath.<init>(PropertyPath.java:94)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:455)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:431)
	at org.springframework.data.mapping.PropertyPath.lambda$from$0(PropertyPath.java:384)
	at java.base/java.util.concurrent.ConcurrentMap.computeIfAbsent(ConcurrentMap.java:330)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:366)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:344)
	at org.springframework.data.repository.query.parser.Part.<init>(Part.java:81)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.lambda$new$0(PartTree.java:259)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.<init>(PartTree.java:260)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.lambda$new$0(PartTree.java:389)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.<init>(PartTree.java:390)
	at org.springframework.data.repository.query.parser.PartTree.<init>(PartTree.java:103)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:101)
	... 63 common frames omitted
2025-06-11 19:17:25.148 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:17:25.149 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:17:25.529 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:17:25.773 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:17:25.797 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
2025-06-11 19:17:25.803 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.mazai.pos.PosApplication.main(PosApplication.java:14)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 38 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:120)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:104)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:431)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$4(RepositoryFactoryBeanSupport.java:350)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:356)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1865)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1814)
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:107)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:128)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:260)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:116)
	... 59 common frames omitted
Caused by: org.springframework.data.mapping.PropertyReferenceException: No property 'orderDate' found for type 'Order'
	at org.springframework.data.mapping.PropertyPath.<init>(PropertyPath.java:94)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:455)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:431)
	at org.springframework.data.mapping.PropertyPath.lambda$from$0(PropertyPath.java:384)
	at java.base/java.util.concurrent.ConcurrentMap.computeIfAbsent(ConcurrentMap.java:330)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:366)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:344)
	at org.springframework.data.repository.query.parser.Part.<init>(Part.java:81)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.lambda$new$0(PartTree.java:259)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.<init>(PartTree.java:260)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.lambda$new$0(PartTree.java:389)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.<init>(PartTree.java:390)
	at org.springframework.data.repository.query.parser.PartTree.<init>(PartTree.java:103)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:101)
	... 63 common frames omitted
2025-06-11 19:17:34.441 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:17:34.442 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:17:34.899 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:17:35.146 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:17:35.196 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
2025-06-11 19:17:35.215 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.mazai.pos.PosApplication.main(PosApplication.java:14)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 38 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:120)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:104)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:431)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$4(RepositoryFactoryBeanSupport.java:350)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:356)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1865)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1814)
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:107)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:128)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:260)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:116)
	... 59 common frames omitted
Caused by: org.springframework.data.mapping.PropertyReferenceException: No property 'orderDate' found for type 'Order'
	at org.springframework.data.mapping.PropertyPath.<init>(PropertyPath.java:94)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:455)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:431)
	at org.springframework.data.mapping.PropertyPath.lambda$from$0(PropertyPath.java:384)
	at java.base/java.util.concurrent.ConcurrentMap.computeIfAbsent(ConcurrentMap.java:330)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:366)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:344)
	at org.springframework.data.repository.query.parser.Part.<init>(Part.java:81)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.lambda$new$0(PartTree.java:259)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.<init>(PartTree.java:260)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.lambda$new$0(PartTree.java:389)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.<init>(PartTree.java:390)
	at org.springframework.data.repository.query.parser.PartTree.<init>(PartTree.java:103)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:101)
	... 63 common frames omitted
2025-06-11 19:17:45.840 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:17:45.840 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:17:46.331 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:17:46.610 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:17:46.638 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
2025-06-11 19:17:46.645 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.mazai.pos.PosApplication.main(PosApplication.java:14)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 38 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:120)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:104)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:431)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$4(RepositoryFactoryBeanSupport.java:350)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:356)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1865)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1814)
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:107)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:128)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:260)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:116)
	... 59 common frames omitted
Caused by: org.springframework.data.mapping.PropertyReferenceException: No property 'orderDate' found for type 'Order'
	at org.springframework.data.mapping.PropertyPath.<init>(PropertyPath.java:94)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:455)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:431)
	at org.springframework.data.mapping.PropertyPath.lambda$from$0(PropertyPath.java:384)
	at java.base/java.util.concurrent.ConcurrentMap.computeIfAbsent(ConcurrentMap.java:330)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:366)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:344)
	at org.springframework.data.repository.query.parser.Part.<init>(Part.java:81)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.lambda$new$0(PartTree.java:259)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.<init>(PartTree.java:260)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.lambda$new$0(PartTree.java:389)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.<init>(PartTree.java:390)
	at org.springframework.data.repository.query.parser.PartTree.<init>(PartTree.java:103)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:101)
	... 63 common frames omitted
2025-06-11 19:17:55.269 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:17:55.269 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:17:55.710 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:17:55.962 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:17:55.986 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
2025-06-11 19:17:55.991 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.mazai.pos.PosApplication.main(PosApplication.java:14)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 38 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:120)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:104)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:431)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$4(RepositoryFactoryBeanSupport.java:350)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:356)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1865)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1814)
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:107)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:128)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:260)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:116)
	... 59 common frames omitted
Caused by: org.springframework.data.mapping.PropertyReferenceException: No property 'orderDate' found for type 'Order'
	at org.springframework.data.mapping.PropertyPath.<init>(PropertyPath.java:94)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:455)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:431)
	at org.springframework.data.mapping.PropertyPath.lambda$from$0(PropertyPath.java:384)
	at java.base/java.util.concurrent.ConcurrentMap.computeIfAbsent(ConcurrentMap.java:330)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:366)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:344)
	at org.springframework.data.repository.query.parser.Part.<init>(Part.java:81)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.lambda$new$0(PartTree.java:259)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.<init>(PartTree.java:260)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.lambda$new$0(PartTree.java:389)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.<init>(PartTree.java:390)
	at org.springframework.data.repository.query.parser.PartTree.<init>(PartTree.java:103)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:101)
	... 63 common frames omitted
2025-06-11 19:18:05.607 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:18:05.607 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:18:06.032 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:18:06.273 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:18:06.304 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
2025-06-11 19:18:06.312 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.mazai.pos.PosApplication.main(PosApplication.java:14)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 38 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:120)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:104)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:431)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$4(RepositoryFactoryBeanSupport.java:350)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:356)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1865)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1814)
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:107)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:128)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:260)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:116)
	... 59 common frames omitted
Caused by: org.springframework.data.mapping.PropertyReferenceException: No property 'orderDate' found for type 'Order'
	at org.springframework.data.mapping.PropertyPath.<init>(PropertyPath.java:94)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:455)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:431)
	at org.springframework.data.mapping.PropertyPath.lambda$from$0(PropertyPath.java:384)
	at java.base/java.util.concurrent.ConcurrentMap.computeIfAbsent(ConcurrentMap.java:330)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:366)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:344)
	at org.springframework.data.repository.query.parser.Part.<init>(Part.java:81)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.lambda$new$0(PartTree.java:259)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.<init>(PartTree.java:260)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.lambda$new$0(PartTree.java:389)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.<init>(PartTree.java:390)
	at org.springframework.data.repository.query.parser.PartTree.<init>(PartTree.java:103)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:101)
	... 63 common frames omitted
2025-06-11 19:18:16.969 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:18:16.970 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:18:17.482 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:18:17.744 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:18:17.787 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
2025-06-11 19:18:17.792 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.mazai.pos.PosApplication.main(PosApplication.java:14)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 38 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:120)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:104)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:431)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$4(RepositoryFactoryBeanSupport.java:350)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:356)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1865)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1814)
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:107)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:128)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:260)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:116)
	... 59 common frames omitted
Caused by: org.springframework.data.mapping.PropertyReferenceException: No property 'orderDate' found for type 'Order'
	at org.springframework.data.mapping.PropertyPath.<init>(PropertyPath.java:94)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:455)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:431)
	at org.springframework.data.mapping.PropertyPath.lambda$from$0(PropertyPath.java:384)
	at java.base/java.util.concurrent.ConcurrentMap.computeIfAbsent(ConcurrentMap.java:330)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:366)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:344)
	at org.springframework.data.repository.query.parser.Part.<init>(Part.java:81)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.lambda$new$0(PartTree.java:259)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.<init>(PartTree.java:260)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.lambda$new$0(PartTree.java:389)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.<init>(PartTree.java:390)
	at org.springframework.data.repository.query.parser.PartTree.<init>(PartTree.java:103)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:101)
	... 63 common frames omitted
2025-06-11 19:18:31.528 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:18:31.528 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:18:32.007 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:18:32.299 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:18:32.327 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
2025-06-11 19:18:32.337 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.mazai.pos.PosApplication.main(PosApplication.java:14)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 38 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:120)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:104)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:431)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$4(RepositoryFactoryBeanSupport.java:350)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:356)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1865)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1814)
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:107)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:128)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:260)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:116)
	... 59 common frames omitted
Caused by: org.springframework.data.mapping.PropertyReferenceException: No property 'orderDate' found for type 'Order'
	at org.springframework.data.mapping.PropertyPath.<init>(PropertyPath.java:94)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:455)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:431)
	at org.springframework.data.mapping.PropertyPath.lambda$from$0(PropertyPath.java:384)
	at java.base/java.util.concurrent.ConcurrentMap.computeIfAbsent(ConcurrentMap.java:330)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:366)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:344)
	at org.springframework.data.repository.query.parser.Part.<init>(Part.java:81)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.lambda$new$0(PartTree.java:259)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.<init>(PartTree.java:260)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.lambda$new$0(PartTree.java:389)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.<init>(PartTree.java:390)
	at org.springframework.data.repository.query.parser.PartTree.<init>(PartTree.java:103)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:101)
	... 63 common frames omitted
2025-06-11 19:18:43.019 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:18:43.019 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:18:43.402 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:18:43.627 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:18:43.657 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
2025-06-11 19:18:43.666 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.mazai.pos.PosApplication.main(PosApplication.java:14)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 38 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:120)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:104)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:431)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$4(RepositoryFactoryBeanSupport.java:350)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:356)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1865)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1814)
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:107)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:128)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:260)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:116)
	... 59 common frames omitted
Caused by: org.springframework.data.mapping.PropertyReferenceException: No property 'orderDate' found for type 'Order'
	at org.springframework.data.mapping.PropertyPath.<init>(PropertyPath.java:94)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:455)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:431)
	at org.springframework.data.mapping.PropertyPath.lambda$from$0(PropertyPath.java:384)
	at java.base/java.util.concurrent.ConcurrentMap.computeIfAbsent(ConcurrentMap.java:330)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:366)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:344)
	at org.springframework.data.repository.query.parser.Part.<init>(Part.java:81)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.lambda$new$0(PartTree.java:259)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.<init>(PartTree.java:260)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.lambda$new$0(PartTree.java:389)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.<init>(PartTree.java:390)
	at org.springframework.data.repository.query.parser.PartTree.<init>(PartTree.java:103)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:101)
	... 63 common frames omitted
2025-06-11 19:18:54.329 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:18:54.329 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:18:54.824 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:18:55.097 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:18:55.138 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
2025-06-11 19:18:55.146 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.mazai.pos.PosApplication.main(PosApplication.java:14)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 38 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:120)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:104)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:431)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$4(RepositoryFactoryBeanSupport.java:350)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:356)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1865)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1814)
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:107)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:128)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:260)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:116)
	... 59 common frames omitted
Caused by: org.springframework.data.mapping.PropertyReferenceException: No property 'orderDate' found for type 'Order'
	at org.springframework.data.mapping.PropertyPath.<init>(PropertyPath.java:94)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:455)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:431)
	at org.springframework.data.mapping.PropertyPath.lambda$from$0(PropertyPath.java:384)
	at java.base/java.util.concurrent.ConcurrentMap.computeIfAbsent(ConcurrentMap.java:330)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:366)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:344)
	at org.springframework.data.repository.query.parser.Part.<init>(Part.java:81)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.lambda$new$0(PartTree.java:259)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.<init>(PartTree.java:260)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.lambda$new$0(PartTree.java:389)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.<init>(PartTree.java:390)
	at org.springframework.data.repository.query.parser.PartTree.<init>(PartTree.java:103)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:101)
	... 63 common frames omitted
2025-06-11 19:19:05.821 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:19:05.821 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:19:06.312 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:19:06.553 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:19:06.629 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
2025-06-11 19:19:06.642 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.mazai.pos.PosApplication.main(PosApplication.java:14)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 38 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:120)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:104)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:431)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$4(RepositoryFactoryBeanSupport.java:350)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:356)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1865)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1814)
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:107)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:128)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:260)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:116)
	... 59 common frames omitted
Caused by: org.springframework.data.mapping.PropertyReferenceException: No property 'orderDate' found for type 'Order'
	at org.springframework.data.mapping.PropertyPath.<init>(PropertyPath.java:94)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:455)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:431)
	at org.springframework.data.mapping.PropertyPath.lambda$from$0(PropertyPath.java:384)
	at java.base/java.util.concurrent.ConcurrentMap.computeIfAbsent(ConcurrentMap.java:330)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:366)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:344)
	at org.springframework.data.repository.query.parser.Part.<init>(Part.java:81)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.lambda$new$0(PartTree.java:259)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.<init>(PartTree.java:260)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.lambda$new$0(PartTree.java:389)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.<init>(PartTree.java:390)
	at org.springframework.data.repository.query.parser.PartTree.<init>(PartTree.java:103)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:101)
	... 63 common frames omitted
2025-06-11 19:19:15.260 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:19:15.260 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:19:15.681 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:19:15.923 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:19:15.948 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
2025-06-11 19:19:15.954 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mobileOrderController': Unsatisfied dependency expressed through field 'salesOrderService': Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.mazai.pos.PosApplication.main(PosApplication.java:14)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'salesOrderService': Unsatisfied dependency expressed through field 'orderRepository': Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderRepository' defined in com.mazai.pos.repository.OrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 38 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); Reason: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:120)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:104)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:431)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$4(RepositoryFactoryBeanSupport.java:350)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:356)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1865)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1814)
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract java.util.List com.mazai.pos.repository.OrderRepository.findByOrderDateBetween(java.time.LocalDateTime,java.time.LocalDateTime); No property 'orderDate' found for type 'Order'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:107)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:128)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:260)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:116)
	... 59 common frames omitted
Caused by: org.springframework.data.mapping.PropertyReferenceException: No property 'orderDate' found for type 'Order'
	at org.springframework.data.mapping.PropertyPath.<init>(PropertyPath.java:94)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:455)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:431)
	at org.springframework.data.mapping.PropertyPath.lambda$from$0(PropertyPath.java:384)
	at java.base/java.util.concurrent.ConcurrentMap.computeIfAbsent(ConcurrentMap.java:330)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:366)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:344)
	at org.springframework.data.repository.query.parser.Part.<init>(Part.java:81)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.lambda$new$0(PartTree.java:259)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.<init>(PartTree.java:260)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.lambda$new$0(PartTree.java:389)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.<init>(PartTree.java:390)
	at org.springframework.data.repository.query.parser.PartTree.<init>(PartTree.java:103)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:101)
	... 63 common frames omitted
2025-06-11 19:20:01.351 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:20:01.352 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:20:01.949 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:20:02.261 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:20:02.446 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 19:20:07.456 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 19:20:07.457 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 19:20:07.516 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:20:07.824 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.513 seconds (process running for 564.434)
2025-06-11 19:20:13.494 [Thread-43] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 19:20:13.494 [Thread-43] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 19:20:13.609 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:20:13.609 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:20:14.161 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:20:14.456 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:20:14.624 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 19:20:19.633 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 19:20:19.633 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 19:20:19.682 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:20:19.941 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.375 seconds (process running for 576.55)
2025-06-11 19:24:30.392 [http-nio-8080-exec-7] ERROR c.m.pos.security.jwt.AuthTokenFilter - Cannot set user authentication: Handler dispatch failed: java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository

jakarta.servlet.ServletException: Handler dispatch failed: java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository

	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.mazai.pos.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository

	at com.mazai.pos.service.SalesOrderService.getOrdersByDateRange(SalesOrderService.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.mazai.pos.service.SalesOrderService$$SpringCGLIB$$0.getOrdersByDateRange(<generated>)
	at com.mazai.pos.controller.MobileOrderController.getTodayOrders(MobileOrderController.java:57)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 92 common frames omitted
2025-06-11 19:24:30.397 [http-nio-8080-exec-7] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Handler dispatch failed: java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository
] with root cause
java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository

	at com.mazai.pos.service.SalesOrderService.getOrdersByDateRange(SalesOrderService.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.mazai.pos.service.SalesOrderService$$SpringCGLIB$$0.getOrdersByDateRange(<generated>)
	at com.mazai.pos.controller.MobileOrderController.getTodayOrders(MobileOrderController.java:57)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at com.mazai.pos.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:107)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-11 19:24:30.404 [http-nio-8080-exec-7] ERROR c.m.p.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-11 19:24:36.759 [http-nio-8080-exec-2] ERROR c.m.pos.security.jwt.AuthTokenFilter - Cannot set user authentication: Handler dispatch failed: java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository

jakarta.servlet.ServletException: Handler dispatch failed: java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository

	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.mazai.pos.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository

	at com.mazai.pos.service.SalesOrderService.getOrdersByDateRange(SalesOrderService.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.mazai.pos.service.SalesOrderService$$SpringCGLIB$$0.getOrdersByDateRange(<generated>)
	at com.mazai.pos.controller.MobileOrderController.getTodayOrders(MobileOrderController.java:57)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 92 common frames omitted
2025-06-11 19:24:36.762 [http-nio-8080-exec-2] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Handler dispatch failed: java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository
] with root cause
java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository

	at com.mazai.pos.service.SalesOrderService.getOrdersByDateRange(SalesOrderService.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.mazai.pos.service.SalesOrderService$$SpringCGLIB$$0.getOrdersByDateRange(<generated>)
	at com.mazai.pos.controller.MobileOrderController.getTodayOrders(MobileOrderController.java:57)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at com.mazai.pos.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:107)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-11 19:24:36.763 [http-nio-8080-exec-2] ERROR c.m.p.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-11 19:24:43.144 [http-nio-8080-exec-2] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-11 19:24:43.144 [http-nio-8080-exec-2] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749641083144}
2025-06-11 19:24:47.501 [Thread-86] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 19:24:47.501 [Thread-86] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 19:24:47.603 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:24:47.603 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:24:48.144 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:24:48.426 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:24:48.564 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 19:24:53.566 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 19:24:53.567 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 19:24:53.627 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:24:53.949 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.383 seconds (process running for 850.558)
2025-06-11 19:25:00.640 [Thread-90] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 销毁ESC/POS网络打印机服务
2025-06-11 19:25:00.641 [Thread-90] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 已断开与ESC/POS网络打印机的连接
2025-06-11 19:25:00.739 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 16028 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by Jeff in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-11 19:25:00.740 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 19:25:01.130 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-11 19:25:01.397 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-11 19:25:01.547 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-11 19:25:06.559 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-11 19:25:06.559 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-11 19:25:06.602 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:25:06.882 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 6.176 seconds (process running for 863.492)
2025-06-11 19:37:38.563 [http-nio-8080-exec-7] ERROR c.m.pos.security.jwt.AuthTokenFilter - Cannot set user authentication: Handler dispatch failed: java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository

jakarta.servlet.ServletException: Handler dispatch failed: java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository

	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.mazai.pos.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository

	at com.mazai.pos.service.SalesOrderService.getOrdersByDateRange(SalesOrderService.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.mazai.pos.service.SalesOrderService$$SpringCGLIB$$0.getOrdersByDateRange(<generated>)
	at com.mazai.pos.controller.MobileOrderController.getTodayOrders(MobileOrderController.java:57)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 92 common frames omitted
2025-06-11 19:37:38.566 [http-nio-8080-exec-7] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Handler dispatch failed: java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository
] with root cause
java.lang.Error: Unresolved compilation problem: 
	The method findByOrderDateBetween(LocalDateTime, LocalDateTime) is undefined for the type OrderRepository

	at com.mazai.pos.service.SalesOrderService.getOrdersByDateRange(SalesOrderService.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.mazai.pos.service.SalesOrderService$$SpringCGLIB$$0.getOrdersByDateRange(<generated>)
	at com.mazai.pos.controller.MobileOrderController.getTodayOrders(MobileOrderController.java:57)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at com.mazai.pos.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:107)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-11 19:37:38.568 [http-nio-8080-exec-7] ERROR c.m.p.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-11 19:37:45.418 [http-nio-8080-exec-2] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-11 19:37:45.418 [http-nio-8080-exec-2] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749641865418}
2025-06-11 19:38:26.271 [http-nio-8080-exec-8] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-11 19:38:26.271 [http-nio-8080-exec-8] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749641906271}

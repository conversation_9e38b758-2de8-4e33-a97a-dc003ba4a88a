import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import PaymentService from '../../services/paymentService';
import { useAuth } from '../../context/AuthContext';
import './PaymentDetail.css';

const PaymentDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const receiptRef = useRef();
  const { isAuthenticated, currentUser } = useAuth();
  const isAdmin = currentUser?.role === 'ADMIN';

  const [payment, setPayment] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 移除额外的认证检查，因为ProtectedRoute已经处理了认证

  useEffect(() => {
    fetchPayment();
  }, [id]);

  const fetchPayment = async () => {
    try {
      setLoading(true);
      const data = await PaymentService.getPaymentById(id);
      setPayment(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch payment details. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this payment record?')) {
      try {
        await PaymentService.deletePayment(id);
        navigate('/payments');
      } catch (err) {
        setError('Failed to delete payment. Please try again.');
        console.error(err);
      }
    }
  };

  // 临时打印功能，等待react-to-print库安装完成后替换
  const handlePrint = () => {
    alert('打印功能暂时不可用，请先安装react-to-print库');
    // 可以使用浏览器的打印功能作为临时替代
    // window.print();
  };

  const getPaymentMethodClass = (method) => {
    // 检查method是否为undefined或null
    if (!method) {
      return 'unknown';
    }
    return method.toLowerCase();
  };

  if (loading) {
    return <div className="loading">Loading payment details...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  if (!payment) {
    return <div className="not-found">Payment not found.</div>;
  }

  return (
    <div className="payment-detail-container">
      <div className="payment-detail-header">
        <h2>Payment Details</h2>
        <div className="payment-actions">
          <Link to="/payments" className="btn-back">Back to Payments</Link>
          <button onClick={handlePrint} className="btn-print">Print Receipt</button>
          {isAdmin && (
            <button onClick={handleDelete} className="btn-delete">Delete</button>
          )}
        </div>
      </div>

      <div className="payment-detail-card">
        <div className="payment-info">
          <div className="payment-header">
            <h3>Payment #{payment.paymentId}</h3>
            <span className={`payment-method ${getPaymentMethodClass(payment.paymentMethod)}`}>
              {payment.paymentMethod}
            </span>
          </div>

          <div className="info-section">
            <div className="info-row">
              <div className="info-label">Order ID:</div>
              <div className="info-value">
                <Link to={`/orders/${payment.order.id}`}>#{payment.order.id}</Link>
              </div>
            </div>

            <div className="info-row">
              <div className="info-label">Amount:</div>
              <div className="info-value total-amount">
                RM {payment.amount ? payment.amount.toFixed(2) : '0.00'}
              </div>
            </div>

            <div className="info-row">
              <div className="info-label">Receipt Status:</div>
              <div className="info-value receipt-status">
                <span className={`receipt-indicator ${payment.receiptIssued ? 'issued' : 'not-issued'}`}></span>
                {payment.receiptIssued ? 'Receipt Issued' : 'No Receipt Issued'}
              </div>
            </div>

            <div className="info-row">
              <div className="info-label">Payment Date:</div>
              <div className="info-value">
                {new Date(payment.createdAt).toLocaleString()}
              </div>
            </div>

            <div className="info-row">
              <div className="info-label">Table Number:</div>
              <div className="info-value">
                {payment.order.tableNumber || 'N/A'}
              </div>
            </div>
          </div>
        </div>

        <div className="payment-items">
          <h4>Payment Items</h4>

          {payment.paymentItems.length === 0 ? (
            <div className="no-items">No items in this payment.</div>
          ) : (
            <div className="table-responsive">
              <table className="items-table">
                <thead>
                  <tr>
                    <th>Product</th>
                    <th>Price</th>
                    <th>Quantity</th>
                    <th>Subtotal</th>
                  </tr>
                </thead>
                <tbody>
                  {payment.paymentItems.map(item => (
                    <tr key={item.id}>
                      <td>{item.orderItem.product.name}</td>
                      <td>RM {item.orderItem.unitPrice ? item.orderItem.unitPrice.toFixed(2) : '0.00'}</td>
                      <td>{item.orderItem.quantity}</td>
                      <td>RM {item.orderItem.subtotal ? item.orderItem.subtotal.toFixed(2) : '0.00'}</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan="3" className="total-label">Total:</td>
                    <td className="total-value">RM {payment.amount ? payment.amount.toFixed(2) : '0.00'}</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Receipt for printing - hidden by default */}
      <div style={{ display: 'none' }}>
        <div ref={receiptRef} className="receipt-container">
          <div className="receipt-header">
            <h3>Mazai POS System</h3>
            <p>Receipt #{payment.paymentId}</p>
            <p>{new Date(payment.createdAt).toLocaleString()}</p>
          </div>

          <div className="receipt-divider"></div>

          <div>
            <p><strong>Order #:</strong> {payment.order.id}</p>
            <p><strong>Table:</strong> {payment.order.tableNumber || 'N/A'}</p>
            <p><strong>Payment Method:</strong> {payment.paymentMethod}</p>
          </div>

          <div className="receipt-divider"></div>

          <div>
            <h4>Items</h4>
            {payment.paymentItems.map(item => (
              <div key={item.id} style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                <span>{item.orderItem.quantity} x {item.orderItem.product.name}</span>
                <span>RM {item.orderItem.subtotal ? item.orderItem.subtotal.toFixed(2) : '0.00'}</span>
              </div>
            ))}
          </div>

          <div className="receipt-divider"></div>

          <div style={{ display: 'flex', justifyContent: 'space-between', fontWeight: 'bold' }}>
            <span>Total:</span>
            <span>RM {payment.amount ? payment.amount.toFixed(2) : '0.00'}</span>
          </div>

          <div className="receipt-footer">
            <p>Thank you for your business!</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentDetail;

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import PaymentService from '../../services/paymentService';
import { useAuth } from '../../context/AuthContext';
import './PaymentList.css';

const PaymentList = () => {
  const navigate = useNavigate();
  const { isAuthenticated, currentUser } = useAuth();
  const isAdmin = currentUser?.role === 'ADMIN';

  const [payments, setPayments] = useState([]);
  const [filteredPayments, setFilteredPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [paymentMethodFilter, setPaymentMethodFilter] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  // 移除额外的认证检查，因为ProtectedRoute已经处理了认证

  useEffect(() => {
    fetchPayments();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [payments, searchTerm, paymentMethodFilter, startDate, endDate]);

  const fetchPayments = async () => {
    try {
      setLoading(true);
      const data = await PaymentService.getAllPayments();
      setPayments(data);
      setFilteredPayments(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch payments. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let result = [...payments];

    // Apply search filter
    if (searchTerm) {
      result = result.filter(payment =>
        payment.order.id.toString().includes(searchTerm) ||
        payment.paymentMethod.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply payment method filter
    if (paymentMethodFilter) {
      result = result.filter(payment =>
        payment.paymentMethod.toLowerCase() === paymentMethodFilter.toLowerCase()
      );
    }

    // Apply date range filter
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999); // Set to end of day

      result = result.filter(payment => {
        const paymentDate = new Date(payment.createdAt);
        return paymentDate >= start && paymentDate <= end;
      });
    }

    setFilteredPayments(result);
  };

  const handleSearch = () => {
    applyFilters();
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this payment?')) {
      try {
        await PaymentService.deletePayment(id);
        fetchPayments();
      } catch (err) {
        setError('Failed to delete payment. Please try again.');
        console.error(err);
      }
    }
  };

  const getPaymentMethodClass = (method) => {
    // 检查method是否为undefined或null
    if (!method) {
      return 'unknown';
    }
    return method.toLowerCase();
  };

  if (loading) {
    return <div className="loading">Loading payments...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  return (
    <div className="payment-list-container">
      <div className="payment-list-header">
        <h2>Payment Records</h2>
        {isAdmin && (
          <button
            onClick={() => navigate('/orders')}
            className="btn-add"
          >
            Process New Payment
          </button>
        )}
      </div>

      <div className="filter-container">
        <select
          className="filter-select"
          value={paymentMethodFilter}
          onChange={(e) => setPaymentMethodFilter(e.target.value)}
        >
          <option value="">All Payment Methods</option>
          <option value="CASH">Cash</option>
          <option value="DUITNOW">DuitNow</option>
        </select>

        <div className="date-filter">
          <span>From:</span>
          <input
            type="date"
            className="date-input"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
          />
          <span>To:</span>
          <input
            type="date"
            className="date-input"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
          />
        </div>
      </div>

      <div className="search-container">
        <input
          type="text"
          placeholder="Search by order ID or payment method..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onKeyPress={handleKeyPress}
          className="search-input"
        />
        <button onClick={handleSearch} className="search-button">Search</button>
      </div>

      {filteredPayments.length === 0 ? (
        <div className="no-payments">No payments found.</div>
      ) : (
        <div className="table-responsive">
          <table className="payment-table">
            <thead>
              <tr>
                <th>Payment ID</th>
                <th>Order ID</th>
                <th>Payment Method</th>
                <th>Amount</th>
                <th>Receipt</th>
                <th>Date</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredPayments.map(payment => (
                <tr key={payment.paymentId}>
                  <td>{payment.paymentId}</td>
                  <td>{payment.order.id}</td>
                  <td>
                    <span className={`payment-method ${getPaymentMethodClass(payment.paymentMethod)}`}>
                      {payment.paymentMethod}
                    </span>
                  </td>
                  <td>RM {payment.amount ? payment.amount.toFixed(2) : '0.00'}</td>
                  <td>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span className={`receipt-status ${payment.receiptIssued ? 'issued' : 'not-issued'}`}></span>
                      {payment.receiptIssued ? 'Issued' : 'Not Issued'}
                    </div>
                  </td>
                  <td>{new Date(payment.createdAt).toLocaleString()}</td>
                  <td className="actions">
                    <button
                      onClick={() => navigate(`/payments/${payment.paymentId}`)}
                      className="btn-view"
                    >
                      View
                    </button>
                    {isAdmin && (
                      <button
                        onClick={() => handleDelete(payment.paymentId)}
                        className="btn-delete"
                      >
                        Delete
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default PaymentList;

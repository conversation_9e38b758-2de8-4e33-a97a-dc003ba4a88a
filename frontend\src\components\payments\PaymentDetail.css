.payment-detail-container {
  padding: 20px;
}

.payment-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.payment-actions {
  display: flex;
  gap: 10px;
}

.btn-back, .btn-delete, .btn-print {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 14px;
}

.btn-back {
  background-color: #f2f2f2;
  color: #333;
}

.btn-delete {
  background-color: #F44336;
  color: white;
}

.btn-print {
  background-color: #2196F3;
  color: white;
}

.btn-back:hover {
  background-color: #e0e0e0;
}

.btn-delete:hover {
  background-color: #da190b;
}

.btn-print:hover {
  background-color: #0b7dda;
}

.payment-detail-card {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.payment-header h3 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.payment-method {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  color: white;
}

.payment-method.cash {
  background-color: #4CAF50;
}

.payment-method.card {
  background-color: #2196F3;
}

.payment-method.unknown {
  background-color: #9E9E9E;
}

.info-section {
  margin-bottom: 30px;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.info-label {
  font-weight: bold;
  width: 150px;
  color: #666;
}

.info-value {
  flex: 1;
}

.total-amount {
  font-weight: bold;
  color: #4CAF50;
}

.receipt-status {
  display: flex;
  align-items: center;
}

.receipt-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.receipt-indicator.issued {
  background-color: #4CAF50;
}

.receipt-indicator.not-issued {
  background-color: #F44336;
}

.payment-items {
  margin-top: 30px;
}

.payment-items h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.items-table {
  width: 100%;
  border-collapse: collapse;
}

.items-table th, .items-table td {
  border: 1px solid #ddd;
  padding: 10px;
  text-align: left;
}

.items-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.items-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.items-table tfoot {
  font-weight: bold;
}

.items-table tfoot .total-label {
  text-align: right;
}

.items-table tfoot .total-value {
  color: #4CAF50;
}

.loading, .error, .not-found {
  padding: 20px;
  text-align: center;
}

.error {
  color: #F44336;
}

.receipt-container {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
  max-width: 400px;
  margin: 0 auto;
}

.receipt-header {
  text-align: center;
  margin-bottom: 20px;
}

.receipt-header h3 {
  margin: 0 0 5px 0;
}

.receipt-header p {
  margin: 5px 0;
  color: #666;
}

.receipt-divider {
  border-top: 1px dashed #ccc;
  margin: 15px 0;
}

.receipt-footer {
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  color: #666;
}

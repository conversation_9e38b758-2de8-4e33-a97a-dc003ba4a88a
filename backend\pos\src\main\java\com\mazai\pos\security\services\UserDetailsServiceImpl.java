package com.mazai.pos.security.services;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mazai.pos.model.User;
import com.mazai.pos.repository.UserRepository;

@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    private static final Logger logger = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    UserRepository userRepository;

    @Override
    @Transactional
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        logger.debug("Loading user details for username: {}", username);

        try {
            User user = userRepository.findByUsername(username)
                    .orElseThrow(() -> {
                        logger.error("User not found with username: {}", username);
                        return new UsernameNotFoundException("User Not Found with username: " + username);
                    });

            logger.debug("User found: id={}, username={}, role={}", user.getId(), user.getUsername(), user.getRole());
            UserDetails userDetails = UserDetailsImpl.build(user);
            logger.debug("UserDetails built successfully for username: {}", username);

            return userDetails;
        } catch (Exception e) {
            logger.error("Error loading user by username {}: {}", username, e.getMessage(), e);
            throw e;
        }
    }
}

import api from './api';

const UserService = {
  getAllUsers: async () => {
    try {
      console.log('UserService: Fetching all users');
      const response = await api.get('/users');
      console.log('UserService: Users fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('UserService: Error fetching users:', error.response?.status, error.message);
      console.error('UserService: Error details:', error.response?.data);
      throw error;
    }
  },

  getUserById: async (id) => {
    try {
      // 移除重复的 /api 前缀
      const response = await api.get(`/users/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching user ${id}:`, error);
      throw error;
    }
  },

  createUser: async (userData) => {
    try {
      // Use auth/register endpoint for creating new users
      const response = await api.post('/auth/register', userData);
      return response.data;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  },

  updateUser: async (id, userData) => {
    try {
      const response = await api.put(`/users/${id}`, userData);
      return response.data;
    } catch (error) {
      console.error(`Error updating user ${id}:`, error);
      throw error;
    }
  },

  deleteUser: async (id) => {
    try {
      await api.delete(`/users/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting user ${id}:`, error);
      throw error;
    }
  }
};

export default UserService;



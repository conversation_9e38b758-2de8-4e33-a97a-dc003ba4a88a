package com.mazai.pos.util;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.TimeZone;

/**
 * 时区工具类
 * 用于处理时区相关的操作和调试
 */
public class TimeZoneUtil {
    
    public static final String MALAYSIA_TIMEZONE = "Asia/Kuala_Lumpur";
    public static final ZoneId MALAYSIA_ZONE_ID = ZoneId.of(MALAYSIA_TIMEZONE);
    
    /**
     * 获取马来西亚当前时间
     */
    public static LocalDateTime getMalaysiaTime() {
        return LocalDateTime.now(MALAYSIA_ZONE_ID);
    }
    
    /**
     * 获取当前系统时区的时间
     */
    public static LocalDateTime getSystemTime() {
        return LocalDateTime.now();
    }
    
    /**
     * 打印时区调试信息
     */
    public static void printTimeZoneInfo() {
        System.out.println("=== 时区调试信息 ===");
        System.out.println("系统默认时区: " + TimeZone.getDefault().getID());
        System.out.println("系统默认时区偏移: " + TimeZone.getDefault().getRawOffset() / (1000 * 60 * 60) + " 小时");
        System.out.println("JVM时区: " + ZoneId.systemDefault());
        
        LocalDateTime systemTime = getSystemTime();
        LocalDateTime malaysiaTime = getMalaysiaTime();
        
        System.out.println("系统时间: " + systemTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("马来西亚时间: " + malaysiaTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        ZonedDateTime zonedTime = ZonedDateTime.now(MALAYSIA_ZONE_ID);
        System.out.println("马来西亚带时区时间: " + zonedTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss XXX")));
        System.out.println("==================");
    }
    
    /**
     * 格式化马来西亚时间为字符串
     */
    public static String formatMalaysiaTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * 格式化马来西亚时间为ISO字符串
     */
    public static String formatMalaysiaTimeISO(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"));
    }
}

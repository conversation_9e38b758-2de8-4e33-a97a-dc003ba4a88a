spring.application.name=pos
# Database Configuration
spring.datasource.url=***********************************************************************************************
spring.datasource.username=root
spring.datasource.password=Weiquan1229#
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool Configuration
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=12
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=1200000

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

# Jackson Date Format Configuration - 使用本地时区
spring.jackson.date-format=yyyy-MM-dd'T'HH:mm:ss.SSS
spring.jackson.time-zone=Asia/Kuala_Lumpur
spring.jackson.serialization.write-dates-as-timestamps=false

# 设置应用程序时区
spring.jpa.properties.hibernate.jdbc.time_zone=Asia/Kuala_Lumpur

# Server Configuration
server.port=8080

# Logging Configuration - 减少噪音日志
logging.level.org.springframework=WARN
logging.level.org.springframework.web=WARN
logging.level.org.springframework.http=WARN
logging.level.org.springframework.security=WARN
logging.level.com.mazai.pos=INFO
logging.level.com.mazai.pos.security.jwt.AuthTokenFilter=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.level.com.zaxxer.hikari.HikariConfig=WARN
logging.level.com.zaxxer.hikari=WARN

# File Logging Configuration
logging.file.name=logs/pos-application.log
logging.file.max-size=10MB
logging.file.max-history=30
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n

# CORS Configuration
spring.web.cors.allowed-origins=http://localhost:3000,http://********:8080,http://********:19000,http://********:19006
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=Authorization,Content-Type,X-Requested-With,Accept,Origin
spring.web.cors.exposed-headers=Authorization
spring.web.cors.allow-credentials=true

# JWT Configuration
pos.app.jwtSecret=posSecretKey123456789012345678901234567890posSecretKey123456789012345678901234567890
pos.app.jwtExpirationMs=86400000

# File Upload Configuration
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=15MB

# File Storage Configuration
file.upload-dir=./uploads/images

# Printer Configuration
printer.enabled=true
printer.type=ESCPOS
printer.name=ESC/POS Network Printer
printer.encoding=GBK
printer.width=32
printer.auto-cut=true

# Default Network Printer Configuration (for backward compatibility)
printer.network.ip=*************
printer.network.port=9100
printer.network.timeout=5000
  
# Multiple Printers Configuration
# Printer 1: Receipt + Kitchen orders for "饮料" and "小食" categories
printer.printers.printer1.ip=*************
printer.printers.printer1.port=9100
printer.printers.printer1.timeout=5000

# Printer 2: Kitchen orders for "煮炒" category only
printer.printers.printer2.ip=*************
printer.printers.printer2.port=9100
printer.printers.printer2.timeout=5000

# Printer 3: Kitchen orders for "面食" category only
printer.printers.printer3.ip=*************
printer.printers.printer3.port=9100
printer.printers.printer3.timeout=5000

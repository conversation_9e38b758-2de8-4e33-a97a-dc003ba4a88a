.payment-list-container {
  padding: 20px;
}

.payment-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.payment-list-header h2 {
  margin: 0;
}

.btn-add {
  background-color: #4CAF50;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 14px;
}

.btn-add:hover {
  background-color: #45a049;
}

.search-container {
  display: flex;
  margin-bottom: 20px;
}

.search-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  font-size: 16px;
}

.search-button {
  background-color: #8b4513;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

.search-button:hover {
  background-color: #6b3100;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  flex-wrap: wrap;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  min-width: 150px;
}

.date-filter {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-filter span {
  font-weight: bold;
  color: #333;
}

.date-input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.loading, .error, .no-payments {
  padding: 20px;
  text-align: center;
}

.error {
  color: #F44336;
}

.table-responsive {
  overflow-x: auto;
}

.payment-table {
  width: 100%;
  border-collapse: collapse;
}

.payment-table th, .payment-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}

.payment-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.payment-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.payment-table tr:hover {
  background-color: #f1f1f1;
}

.actions {
  display: flex;
  gap: 5px;
}

.btn-view, .btn-delete {
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: white;
}

.btn-view {
  background-color: #2196F3;
}

.btn-delete {
  background-color: #F44336;
}

.btn-view:hover {
  background-color: #0b7dda;
}

.btn-delete:hover {
  background-color: #d32f2f;
}

.payment-method {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.payment-method.cash {
  background-color: #4CAF50;
  color: white;
}

.payment-method.duitnow {
  background-color: #FF6B35;
  color: white;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);
}

.payment-method.card {
  background-color: #2196F3;
  color: white;
}

.payment-method.unknown {
  background-color: #9E9E9E;
  color: white;
}

.receipt-status {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
}

.receipt-status.issued {
  background-color: #4CAF50;
}

.receipt-status.not-issued {
  background-color: #F44336;
}



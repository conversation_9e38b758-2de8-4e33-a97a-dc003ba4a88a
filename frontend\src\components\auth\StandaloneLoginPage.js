import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import './StandaloneLoginPage.css';

const StandaloneLoginPage = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const { login } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('Login form submitted');

    if (!formData.username || !formData.password) {
      console.log('Form validation failed: missing username or password');
      setError('Please enter both username and password');
      return;
    }

    try {
      console.log('Attempting login with username:', formData.username);
      setError('');
      setLoading(true);

      // Clear any existing auth data before login attempt
      localStorage.removeItem('user');

      // Attempt login
      const user = await login(formData.username, formData.password);

      // Verify we got a token
      if (!user || (!user.token && !user.accessToken)) {
        console.error('Login succeeded but no token received');
        setError('Authentication failed: No token received from server');
        return;
      }

      console.log('Login successful, token received');
      console.log('Navigating to home page immediately');

      navigate('/');

      // 如果直接导航不起作用，我们可以尝试使用window.location
      console.log('Also trying window.location as fallback');
      setTimeout(() => {
        if (window.location.pathname === '/login') {
          console.log('Still on login page, forcing navigation with window.location');
          window.location.href = '/';
        }
      }, 500);
    } catch (err) {
      console.error('Login error:', err);

      if (err.response) {
        console.error('Error status:', err.response.status);
        console.error('Error details:', err.response.data);

        // Handle specific error cases
        if (err.response.status === 401) {
          setError('Invalid username or password. Please try again.');
        } else if (err.response.status === 403) {
          setError('You do not have permission to access this system.');
        } else {
          setError(
            err.response.data?.message ||
            'Failed to login. Please check your credentials and try again.'
          );
        }
      } else if (err.request) {
        // Request was made but no response received
        console.error('No response received:', err.request);
        setError('Server not responding. Please try again later.');
      } else {
        // Error in setting up the request
        setError(err.message || 'An unexpected error occurred. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="standalone-login-container">
      <div className="login-form-container">
        <h2>Login to Mazai POS System</h2>

        {error && <div className="error-message">{error}</div>}

        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="username">Username</label>
            <input
              type="text"
              id="username"
              name="username"
              value={formData.username}
              onChange={handleChange}
              disabled={loading}
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              disabled={loading}
            />
          </div>

          <button
            type="submit"
            className="login-button"
            disabled={loading}
          >
            {loading ? 'Logging in...' : 'Login'}
          </button>
        </form>

        <div className="login-footer">
          <p>
            Don't have an account? Contact your administrator.
          </p>
        </div>
      </div>
    </div>
  );
};

export default StandaloneLoginPage;

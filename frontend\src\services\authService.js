import api from './api';

const AuthService = {
  login: async (username, password) => {
    try {
      console.log('Attempting login for user:', username);
      const response = await api.post('/auth/login', { username, password });
      console.log('Login response structure:', Object.keys(response.data));
      console.log('Login response data:', JSON.stringify(response.data, null, 2));

      // Check for token in different possible properties
      const token = response.data.token || response.data.accessToken;

      if (token) {
        // Create a standardized user object with token
        const userData = {
          ...response.data,
          token: token // Ensure token is always available under the 'token' property
        };

        console.log('Token received, storing in localStorage. Token length:', token.length);

        // Store the user data in localStorage
        localStorage.setItem('user', JSON.stringify(userData));

        // Verify the data was stored correctly
        try {
          const storedUser = JSON.parse(localStorage.getItem('user'));
          console.log('Stored user verified. Token available:', !!storedUser.token);

          // Set the token in axios default headers immediately
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
          console.log('Default Authorization header set for future requests');
        } catch (storageError) {
          console.error('Error verifying stored user:', storageError);
        }
      } else {
        console.error('No token in response data. Available fields:', Object.keys(response.data));
      }

      return response.data;
    } catch (error) {
      console.error('Login error:', error.response?.data || error.message);
      if (error.response) {
        console.error('Error status:', error.response.status);
        console.error('Error details:', error.response.data);
      }
      throw error;
    }
  },

  logout: () => {
    localStorage.removeItem('user');
  },

  register: async (username, email, password, role = 'USER') => {
    try {
      const response = await api.post('/auth/register', {
        username,
        email,
        password,
        role
      });
      return response.data;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  },

  getCurrentUser: () => {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      return JSON.parse(userStr);
    }
    return null;
  },

  isAuthenticated: () => {
    return localStorage.getItem('user') !== null;
  },

  checkAuthStatus: async () => {
    try {
      console.log('Checking auth status with server');
      const response = await api.get('/test/auth-status');
      console.log('Auth status response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error checking auth status:', error);
      throw error;
    }
  }
};

export default AuthService;



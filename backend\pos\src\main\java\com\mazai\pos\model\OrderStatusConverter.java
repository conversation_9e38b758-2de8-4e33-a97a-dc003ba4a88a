package com.mazai.pos.model;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * 自定义转换器，用于处理VARCHAR类型的status字段与OrderStatus枚举之间的转换
 */
@Converter(autoApply = true)
public class OrderStatusConverter implements AttributeConverter<Order.OrderStatus, String> {

    @Override
    public String convertToDatabaseColumn(Order.OrderStatus attribute) {
        if (attribute == null) {
            return null;
        }
        // 将枚举值转换为数据库列值（使用小写以保持一致性）
        return attribute.name().toLowerCase();
    }

    @Override
    public Order.OrderStatus convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        
        // 尝试不同的匹配方式
        try {
            // 1. 直接匹配（如果数据库中存储的是大写）
            return Order.OrderStatus.valueOf(dbData);
        } catch (IllegalArgumentException e) {
            try {
                // 2. 转换为大写后匹配（如果数据库中存储的是小写或混合大小写）
                return Order.OrderStatus.valueOf(dbData.toUpperCase());
            } catch (IllegalArgumentException ex) {
                // 3. 特殊处理常见的值
                if (dbData.equalsIgnoreCase("open") || dbData.equalsIgnoreCase("OPEN")) {
                    return Order.OrderStatus.OPEN;
                } else if (dbData.equalsIgnoreCase("closed") || dbData.equalsIgnoreCase("CLOSED")) {
                    return Order.OrderStatus.CLOSED;
                }
                
                // 4. 如果所有尝试都失败，记录错误并返回默认值
                System.err.println("无法转换数据库值 '" + dbData + "' 到OrderStatus枚举。使用默认值OPEN。");
                return Order.OrderStatus.OPEN;
            }
        }
    }
}

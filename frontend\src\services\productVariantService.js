import api from './api';

const ProductVariantService = {
  getAllVariants: async () => {
    try {
      console.log('ProductVariantService: Fetching all variants');
      const response = await api.get('/product-variants');
      console.log('ProductVariantService: Variants fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('ProductVariantService: Error fetching variants:', error.response?.status, error.message);
      console.error('ProductVariantService: Error details:', error.response?.data);
      throw error;
    }
  },

  getVariantById: async (id) => {
    try {
      console.log(`ProductVariantService: Fetching variant with id ${id}`);
      const response = await api.get(`/product-variants/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching product variant ${id}:`, error);
      throw error;
    }
  },

  getVariantsByProduct: async (productId) => {
    try {
      console.log(`ProductVariantService: Fetching variants for product ${productId}`);
      const response = await api.get(`/product-variants/product/${productId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching variants for product ${productId}:`, error);
      throw error;
    }
  },

  createVariant: async (variantData) => {
    try {
      console.log('ProductVariantService: Creating new variant');
      const response = await api.post('/product-variants', variantData);
      return response.data;
    } catch (error) {
      console.error('Error creating product variant:', error);
      throw error;
    }
  },

  updateVariant: async (id, variantData) => {
    try {
      console.log(`ProductVariantService: Updating variant ${id}`);
      const response = await api.put(`/product-variants/${id}`, variantData);
      return response.data;
    } catch (error) {
      console.error(`Error updating product variant ${id}:`, error);
      throw error;
    }
  },

  deleteVariant: async (id) => {
    try {
      console.log(`ProductVariantService: Deleting variant ${id}`);
      await api.delete(`/product-variants/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting product variant ${id}:`, error);
      throw error;
    }
  }
};

export default ProductVariantService;

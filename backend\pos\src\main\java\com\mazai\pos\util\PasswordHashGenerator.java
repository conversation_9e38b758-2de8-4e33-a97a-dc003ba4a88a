package com.mazai.pos.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * Utility class to generate BCrypt password hashes for testing
 */
public class PasswordHashGenerator {
    
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        
        // Common passwords for testing
        String[] passwords = {
            "admin123",
            "jeff123", 
            "test123",
            "user123",
            "password",
            "secret"
        };
        
        System.out.println("BCrypt Password Hashes:");
        System.out.println("========================");
        
        for (String password : passwords) {
            String hash = encoder.encode(password);
            System.out.println(password + " -> " + hash);
        }
        
        // Test specific password if provided as argument
        if (args.length > 0) {
            System.out.println("\nCustom password:");
            System.out.println("================");
            String customPassword = args[0];
            String customHash = encoder.encode(customPassword);
            System.out.println(customPassword + " -> " + customHash);
        }
        
        // Verify some hashes
        System.out.println("\nVerification Test:");
        System.out.println("==================");
        String testPassword = "admin123";
        String testHash = encoder.encode(testPassword);
        boolean matches = encoder.matches(testPassword, testHash);
        System.out.println("Password: " + testPassword);
        System.out.println("Hash: " + testHash);
        System.out.println("Matches: " + matches);
    }
}

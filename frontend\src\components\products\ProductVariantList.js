import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import ProductVariantService from '../../services/productVariantService';
import { useAuth } from '../../context/AuthContext';
import './ProductVariantList.css';

const ProductVariantList = () => {
  const [variants, setVariants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [categories, setCategories] = useState([]);
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const isAdmin = currentUser?.role === 'ADMIN';

  useEffect(() => {
    console.log('ProductVariantList: Component mounted, fetching variants');
    fetchVariants();
  }, []);

  const fetchVariants = async () => {
    try {
      console.log('ProductVariantList: Fetching variants');
      setLoading(true);
      const data = await ProductVariantService.getAllVariants();
      console.log('ProductVariantList: Variants fetched successfully:', data);
      setVariants(data);

      // 提取所有唯一的产品类别
      const uniqueCategories = [...new Set(data.map(variant =>
        variant.product.category).filter(Boolean))];
      setCategories(uniqueCategories);

      setError(null);
    } catch (err) {
      console.error('ProductVariantList: Error fetching variants:', err.message);
      console.error('ProductVariantList: Error details:', err.response?.data);
      setError('Failed to fetch product variants. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this variant?')) {
      try {
        await ProductVariantService.deleteVariant(id);
        setVariants(variants.filter(variant => variant.id !== id));
      } catch (err) {
        setError('Failed to delete variant. Please try again.');
        console.error(err);
      }
    }
  };

  const handleSearch = async () => {
    try {
      setLoading(true);

      // 如果没有搜索词和类别，获取所有变体
      if (!searchTerm.trim() && !selectedCategory) {
        fetchVariants();
        return;
      }

      // 获取所有变体，然后在前端进行过滤
      const allVariants = await ProductVariantService.getAllVariants();

      // 根据搜索条件过滤变体
      let filtered = allVariants;

      // 如果有搜索词，按名称或产品名称过滤
      if (searchTerm.trim()) {
        filtered = filtered.filter(variant =>
          variant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          variant.product.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      // 如果选择了类别，按产品类别过滤
      if (selectedCategory) {
        filtered = filtered.filter(variant =>
          variant.product.category === selectedCategory
        );
      }

      setVariants(filtered);
      setError(null);
    } catch (err) {
      setError('Failed to search variants. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const getVariantTypeClass = (type) => {
    return type.toLowerCase();
  };

  if (loading) {
    return <div className="loading">Loading product variants...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  return (
    <div className="product-list-container">
      <div className="product-list-header">
        <h2>Product Variants</h2>
        {isAdmin && (
          <Link to="/product-variants/new" className="btn-add">Add New Variant</Link>
        )}
      </div>

      <div className="search-container">
        <input
          type="text"
          placeholder="Search by name..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
          className="search-input"
        />
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="category-select"
        >
          <option value="">All Categories</option>
          {categories.map(category => (
            <option key={category} value={category}>{category}</option>
          ))}
        </select>
        <button onClick={handleSearch} className="search-button">Search</button>
      </div>

      {variants.length === 0 ? (
        <div className="no-products">No product variants found.</div>
      ) : (
        <div className="table-responsive">
          <table className="product-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Product</th>
                <th>Variant Name</th>
                <th>Type</th>
                <th>Price Adjustment</th>
                <th>Default</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {variants.map(variant => (
                <tr key={variant.id}>
                  <td>{variant.id}</td>
                  <td>{variant.product.name}</td>
                  <td>{variant.name}</td>
                  <td>
                    <span className={`variant-type ${getVariantTypeClass(variant.variantType)}`}>
                      {variant.variantType}
                    </span>
                  </td>
                  <td>RM {variant.priceAdjustment.toFixed(2)}</td>
                  <td>
                    {variant.isDefault && (
                      <span className="default-badge">Default</span>
                    )}
                  </td>
                  <td className="actions">
                    <button
                      onClick={() => navigate(`/product-variants/${variant.id}`)}
                      className="btn-view"
                    >
                      View
                    </button>
                    {isAdmin && (
                      <>
                        <button
                          onClick={() => navigate(`/product-variants/edit/${variant.id}`)}
                          className="btn-edit"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDelete(variant.id)}
                          className="btn-delete"
                        >
                          Delete
                        </button>
                      </>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default ProductVariantList;

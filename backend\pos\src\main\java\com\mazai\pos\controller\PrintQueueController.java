package com.mazai.pos.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mazai.pos.service.impl.PrintQueueServiceImpl;

/**
 * 打印队列监控控制器
 * 提供打印队列状态和统计信息的API
 */
@RestController
@RequestMapping("/api/print-queue")
public class PrintQueueController {

    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(PrintQueueController.class);

    @Autowired
    private PrintQueueServiceImpl printQueueService;

    /**
     * 获取打印队列状态
     * @return 队列状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getQueueStatus() {
        Map<String, Object> status = new HashMap<>();

        status.put("queueSize", printQueueService.getQueueSize());
        status.put("totalProcessed", printQueueService.getTotalProcessed());
        status.put("successfulPrints", printQueueService.getSuccessfulPrints());
        status.put("failedPrints", printQueueService.getFailedPrints());
        status.put("statistics", printQueueService.getPrintStatistics());

        // 计算成功率
        int total = printQueueService.getTotalProcessed();
        double successRate = total > 0 ?
            (double) printQueueService.getSuccessfulPrints() / total * 100 : 0;
        status.put("successRate", Math.round(successRate * 10.0) / 10.0);

        return ResponseEntity.ok(status);
    }

    /**
     * 清空打印队列
     * @return 操作结果
     */
    @PostMapping("/clear")
    public ResponseEntity<Map<String, Object>> clearQueue() {
        Map<String, Object> result = new HashMap<>();

        try {
            int queueSizeBefore = printQueueService.getQueueSize();
            printQueueService.clearQueue();

            result.put("success", true);
            result.put("message", "打印队列已清空");
            result.put("clearedItems", queueSizeBefore);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "清空队列失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 重置打印统计信息
     * @return 操作结果
     */
    @PostMapping("/reset-statistics")
    public ResponseEntity<Map<String, Object>> resetStatistics() {
        Map<String, Object> result = new HashMap<>();

        try {
            printQueueService.resetStatistics();

            result.put("success", true);
            result.put("message", "打印统计信息已重置");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "重置统计信息失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 获取详细统计信息
     * @return 详细统计数据
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getDetailedStatistics() {
        Map<String, Object> stats = new HashMap<>();

        int total = printQueueService.getTotalProcessed();
        int successful = printQueueService.getSuccessfulPrints();
        int failed = printQueueService.getFailedPrints();

        stats.put("totalProcessed", total);
        stats.put("successfulPrints", successful);
        stats.put("failedPrints", failed);
        stats.put("currentQueueSize", printQueueService.getQueueSize());

        if (total > 0) {
            stats.put("successRate", Math.round((double) successful / total * 1000.0) / 10.0);
            stats.put("failureRate", Math.round((double) failed / total * 1000.0) / 10.0);
        } else {
            stats.put("successRate", 0.0);
            stats.put("failureRate", 0.0);
        }

        stats.put("statisticsText", printQueueService.getPrintStatistics());

        return ResponseEntity.ok(stats);
    }
}

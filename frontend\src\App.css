.App {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.App-header {
  background-color: #8b4513; /* Brown color for Mazai POS theme */
  padding: 1rem 2rem;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.App-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.main-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 1.5rem;
}

.main-nav a {
  color: white;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.main-nav a:hover {
  border-color: #f0ad4e; /* Amber color for hover effect */
  color: #f0ad4e;
}

.user-menu {
  display: flex;
  align-items: center;
  margin-left: auto;
  color: #fff;
}

.user-menu span {
  margin-right: 10px;
  font-weight: 500;
}

.logout-btn {
  background-color: transparent;
  color: white;
  border: 1px solid #f0ad4e;
  padding: 5px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.logout-btn:hover {
  background-color: #f0ad4e;
  color: #8b4513;
}



.App-content {
  flex: 1;
  padding: 2rem;
  background-color: #f9f5f0; /* Light beige background */
}

.App-footer {
  background-color: #8b4513;
  color: white;
  text-align: center;
  padding: 1rem;
  font-size: 0.9rem;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .App-header {
    flex-direction: column;
    padding: 1rem;
  }

  .App-header h1 {
    margin-bottom: 1rem;
  }

  .main-nav ul {
    gap: 1rem;
  }

  .App-content {
    padding: 1rem;
  }
}


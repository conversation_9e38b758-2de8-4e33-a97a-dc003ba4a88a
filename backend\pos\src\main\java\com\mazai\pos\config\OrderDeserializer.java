package com.mazai.pos.config;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.mazai.pos.model.Order;
import com.mazai.pos.model.OrderItem;
import com.mazai.pos.model.Product;

/**
 * 自定义Order类的反序列化器，用于处理前端传来的JSON数据
 */
public class OrderDeserializer extends StdDeserializer<Order> {

    private static final long serialVersionUID = 1L;

    private final ObjectMapper mapper;

    public OrderDeserializer() {
        this(null);
    }

    public OrderDeserializer(Class<?> vc) {
        super(vc);
        this.mapper = new ObjectMapper();
        this.mapper.registerModule(new JavaTimeModule());
    }

    @Override
    public Order deserialize(JsonParser p, DeserializationContext ctxt)
            throws IOException, JsonProcessingException {

        JsonNode node = p.getCodec().readTree(p);
        Order order = new Order();

        // 解析基本字段
        if (node.has("id") && !node.get("id").isNull()) {
            order.setId(node.get("id").asLong());
        }

        // orderDate字段已删除，不再处理

        if (node.has("status") && !node.get("status").isNull()) {
            String statusStr = node.get("status").asText();
            try {
                Order.OrderStatus status = Order.OrderStatus.valueOf(statusStr.toUpperCase());
                order.setStatus(status);
            } catch (IllegalArgumentException e) {
                System.err.println("Invalid status value: " + statusStr + ". Using default OPEN.");
                order.setStatus(Order.OrderStatus.OPEN);
            }
        }

        if (node.has("tableNumber") && !node.get("tableNumber").isNull()) {
            // 处理不同类型的tableNumber值
            JsonNode tableNode = node.get("tableNumber");
            if (tableNode.isInt()) {
                // 如果是整数，转换为字符串
                order.setTableNumber(String.valueOf(tableNode.asInt()));
            } else if (tableNode.isTextual()) {
                // 如果是文本，直接使用
                order.setTableNumber(tableNode.asText());
            } else {
                // 其他情况，转换为字符串
                order.setTableNumber(tableNode.toString());
            }
        }

        if (node.has("paidAt") && !node.get("paidAt").isNull()) {
            try {
                String dateStr = node.get("paidAt").asText();
                LocalDateTime paidAt;

                // 尝试多种日期格式解析
                try {
                    // 尝试ISO格式 (2023-01-01T12:00:00.000Z)
                    paidAt = mapper.readValue("\"" + dateStr + "\"", LocalDateTime.class);
                } catch (Exception e1) {
                    try {
                        // 尝试简单日期格式 (2023-01-01)
                        if (dateStr.length() == 10 && dateStr.contains("-")) {
                            paidAt = LocalDateTime.parse(dateStr + "T00:00:00");
                        } else {
                            // 尝试时间戳格式
                            long timestamp = Long.parseLong(dateStr);
                            paidAt = LocalDateTime.ofInstant(
                                Instant.ofEpochMilli(timestamp),
                                ZoneId.of("Asia/Kuala_Lumpur")
                            );
                        }
                    } catch (Exception e2) {
                        System.err.println("Failed to parse paidAt with multiple formats: " + dateStr);
                        throw e2;
                    }
                }

                order.setPaidAt(paidAt);
                System.out.println("Successfully parsed paidAt: " + paidAt);
            } catch (Exception e) {
                System.err.println("Error parsing paidAt: " + e.getMessage());
            }
        }

        if (node.has("totalAmount") && !node.get("totalAmount").isNull()) {
            try {
                BigDecimal amount = new BigDecimal(node.get("totalAmount").asText());
                order.setTotalAmount(amount);
            } catch (NumberFormatException e) {
                System.err.println("Error parsing totalAmount: " + e.getMessage());
                order.setTotalAmount(BigDecimal.ZERO);
            }
        } else {
            order.setTotalAmount(BigDecimal.ZERO);
        }

        // 解析订单项
        List<OrderItem> orderItems = new ArrayList<>();
        if (node.has("orderItems") && node.get("orderItems").isArray()) {
            ArrayNode itemsNode = (ArrayNode) node.get("orderItems");
            for (JsonNode itemNode : itemsNode) {
                OrderItem item = new OrderItem();

                if (itemNode.has("id") && !itemNode.get("id").isNull()) {
                    item.setId(itemNode.get("id").asLong());
                }

                // 设置产品引用
                if (itemNode.has("product") && !itemNode.get("product").isNull()) {
                    JsonNode productNode = itemNode.get("product");
                    if (productNode.has("id") && !productNode.get("id").isNull()) {
                        Product product = new Product();
                        product.setId(productNode.get("id").asLong());
                        item.setProduct(product);
                    }
                }

                if (itemNode.has("quantity") && !itemNode.get("quantity").isNull()) {
                    item.setQuantity(itemNode.get("quantity").asInt());
                } else {
                    item.setQuantity(1);
                }

                if (itemNode.has("unitPrice") && !itemNode.get("unitPrice").isNull()) {
                    try {
                        BigDecimal price = new BigDecimal(itemNode.get("unitPrice").asText());
                        item.setUnitPrice(price);
                    } catch (NumberFormatException e) {
                        System.err.println("Error parsing unitPrice: " + e.getMessage());
                        item.setUnitPrice(BigDecimal.ZERO);
                    }
                }

                if (itemNode.has("subtotal") && !itemNode.get("subtotal").isNull()) {
                    try {
                        BigDecimal subtotal = new BigDecimal(itemNode.get("subtotal").asText());
                        item.setSubtotal(subtotal);
                    } catch (NumberFormatException e) {
                        System.err.println("Error parsing subtotal: " + e.getMessage());
                        // 如果单价和数量已设置，计算小计
                        if (item.getUnitPrice() != null && item.getQuantity() != null) {
                            item.calculateSubtotal();
                        } else {
                            item.setSubtotal(BigDecimal.ZERO);
                        }
                    }
                } else if (item.getUnitPrice() != null && item.getQuantity() != null) {
                    item.calculateSubtotal();
                }

                if (itemNode.has("isServed") && !itemNode.get("isServed").isNull()) {
                    item.setIsServed(itemNode.get("isServed").asBoolean());
                } else {
                    item.setIsServed(false);
                }

                if (itemNode.has("variantIds") && !itemNode.get("variantIds").isNull()) {
                    item.setVariantIds(itemNode.get("variantIds").asText());
                }

                if (itemNode.has("remark") && !itemNode.get("remark").isNull()) {
                    item.setRemark(itemNode.get("remark").asText());
                }

                if (itemNode.has("orderStatus") && !itemNode.get("orderStatus").isNull()) {
                    item.setOrderStatus(itemNode.get("orderStatus").asText());
                }

                // 设置订单引用
                item.setOrder(order);
                orderItems.add(item);
            }
        }

        order.setOrderItems(orderItems);
        return order;
    }
}

import api from './api';

const OrderService = {
  getAllOrders: async () => {
    try {
      console.log('OrderService: Fetching all orders');
      const response = await api.get('/orders');
      console.log('OrderService: Orders fetched successfully:', response.data);

      // 确保返回的数据是数组
      if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.error('OrderService: Expected array but got:', typeof response.data, response.data);
        return []; // 返回空数组而不是抛出错误
      }
    } catch (error) {
      console.error('OrderService: Error fetching orders:', error.response?.status, error.message);
      console.error('OrderService: Error details:', error.response?.data);
      throw error;
    }
  },

  getOrderById: async (id) => {
    try {
      const response = await api.get(`/orders/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching order ${id}:`, error);
      throw error;
    }
  },

  getOrdersByUser: async (userId) => {
    try {
      const response = await api.get(`/orders/user/${userId}`);

      // 确保返回的数据是数组
      if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.error(`Error: Expected array but got:`, typeof response.data, response.data);
        return []; // 返回空数组而不是抛出错误
      }
    } catch (error) {
      console.error(`Error fetching orders for user ${userId}:`, error);
      throw error;
    }
  },

  getOrdersByDateRange: async (startDate, endDate) => {
    try {
      const response = await api.get(`/orders/date-range?startDate=${startDate}&endDate=${endDate}`);

      // 确保返回的数据是数组
      if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.error(`Error: Expected array but got:`, typeof response.data, response.data);
        return []; // 返回空数组而不是抛出错误
      }
    } catch (error) {
      console.error(`Error fetching orders by date range:`, error);
      throw error;
    }
  },

  getOrdersByStatus: async (status) => {
    try {
      const response = await api.get(`/orders/status/${status}`);

      // 确保返回的数据是数组
      if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.error(`Error: Expected array but got:`, typeof response.data, response.data);
        return []; // 返回空数组而不是抛出错误
      }
    } catch (error) {
      console.error(`Error fetching orders by status ${status}:`, error);
      throw error;
    }
  },

  getOrderItems: async (orderId) => {
    try {
      const response = await api.get(`/orders/${orderId}/items`);

      // 确保返回的数据是数组
      if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.error(`Error: Expected array but got:`, typeof response.data, response.data);
        return []; // 返回空数组而不是抛出错误
      }
    } catch (error) {
      console.error(`Error fetching items for order ${orderId}:`, error);
      throw error;
    }
  },

  createOrder: async (orderData) => {
    try {
      const response = await api.post('/orders', orderData);
      return response.data;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  },

  updateOrder: async (id, orderData) => {
    try {
      const response = await api.put(`/orders/${id}`, orderData);
      return response.data;
    } catch (error) {
      console.error(`Error updating order ${id}:`, error);
      throw error;
    }
  },

  deleteOrder: async (id) => {
    try {
      await api.delete(`/orders/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting order ${id}:`, error);
      throw error;
    }
  }
};

export default OrderService;

package com.mazai.pos.repository;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.mazai.pos.model.Order;

@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {
    List<Order> findByStatus(Order.OrderStatus status);
    List<Order> findByUpdatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<Order> findByTableNumber(String tableNumber);
}

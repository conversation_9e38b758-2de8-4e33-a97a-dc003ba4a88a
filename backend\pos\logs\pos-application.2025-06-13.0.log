2025-06-13 21:56:26.050 [restartedMain] INFO  com.mazai.pos.PosApplication - Starting PosApplication using Java 21.0.7 with PID 15008 (D:\Jeff\PROJECT\POS\backend\pos\target\classes started by <PERSON> in D:\Jeff\PROJECT\POS\backend\pos)
2025-06-13 21:56:26.056 [restartedMain] INFO  com.mazai.pos.PosApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 21:56:29.063 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-13 21:56:30.595 [restartedMain] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-13 21:56:30.778 [restartedMain] INFO  c.m.p.s.i.EscPosPrinterServiceImpl - 初始化ESC/POS网络打印机连接
2025-06-13 21:56:35.799 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - 测试连接失败: Connect timed out
2025-06-13 21:56:35.801 [restartedMain] ERROR c.m.p.s.i.EscPosPrinterServiceImpl - ESC/POS网络打印机连接失败
2025-06-13 21:56:35.910 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-13 21:56:36.601 [restartedMain] INFO  com.mazai.pos.PosApplication - Started PosApplication in 11.235 seconds (process running for 11.729)
2025-06-13 21:58:36.432 [http-nio-8080-exec-2] ERROR com.mazai.pos.security.jwt.JwtUtils - JWT token is expired: exp=1749724383, now=1749823116
2025-06-13 21:58:36.868 [http-nio-8080-exec-2] INFO  c.m.pos.controller.AuthController - User admin logged in successfully

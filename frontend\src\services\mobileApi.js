import axios from 'axios';

const MOBILE_API_URL = 'http://localhost:8080/mobile';

const mobileApi = axios.create({
  baseURL: MOBILE_API_URL,
  headers: {
    'Content-Type': 'application/json',
  }
});

// Add an interceptor to handle errors but without authentication redirection
mobileApi.interceptors.response.use(
  response => {
    console.log('Mobile API Response:', response.config.url, response.status);
    return response;
  },
  error => {
    console.error('Mobile API Error:', error.config?.url, error.response?.status, error.message);
    console.error('Error details:', error.response?.data);
    return Promise.reject(error);
  }
);

export default mobileApi;

.user-detail-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.user-actions {
  display: flex;
  gap: 10px;
}

.btn-back, .btn-edit, .btn-delete {
  padding: 8px 15px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  text-decoration: none;
}

.btn-back {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.btn-back:hover {
  background-color: #e0e0e0;
}

.btn-edit {
  background-color: #2196F3;
  color: white;
  border: none;
}

.btn-edit:hover {
  background-color: #0b7dda;
}

.btn-delete {
  background-color: #f44336;
  color: white;
  border: none;
}

.btn-delete:hover {
  background-color: #d32f2f;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-group {
  display: flex;
  align-items: center;
}

.info-label {
  width: 120px;
  font-weight: 600;
  color: #555;
}

.info-value {
  flex: 1;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-badge.inactive {
  background-color: #ffebee;
  color: #c62828;
}

.loading, .error {
  text-align: center;
  padding: 20px;
}

.error {
  color: #f44336;
}

.user-detail-section {
  margin-top: 30px;
}

.user-detail-section h3 {
  margin-bottom: 15px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.empty-state {
  text-align: center;
  padding: 20px;
  color: #757575;
  font-style: italic;
}

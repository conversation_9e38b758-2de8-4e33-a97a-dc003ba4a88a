import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import ProductService from '../../services/productService';
import './ProductForm.css';

const ProductForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditMode = !!id;

  const [formData, setFormData] = useState({
    name: '',
    altname: '',
    description: '',
    price: '',
    category: '',
    isAvailable: true,
    imageUrl: ''
  });

  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});

  useEffect(() => {
    if (isEditMode) {
      fetchProduct();
    }
  }, [id]);

  const fetchProduct = async () => {
    try {
      setLoading(true);
      const product = await ProductService.getProductById(id);
      setFormData({
        name: product.name,
        altname: product.altname || '',
        description: product.description || '',
        price: product.price.toString(),
        category: product.category || '',
        isAvailable: product.isAvailable,
        imageUrl: product.imageUrl || ''
      });

      // Set image preview if product has an image
      if (product.imageUrl) {
        setImagePreview(product.imageUrl);
      }
    } catch (err) {
      setError('Failed to fetch product details. Please try again.');
      console.error('Error in fetchProduct:', err);
      console.error('Error response:', err.response?.data);
      console.error('Error status:', err.response?.status);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear validation error when field is edited
    if (validationErrors[name]) {
      setValidationErrors({
        ...validationErrors,
        [name]: null
      });
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImageFile(file);

      // Create a preview URL for the image
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = 'Product name is required';
    }

    if (!formData.price.trim()) {
      errors.price = 'Price is required';
    } else if (isNaN(parseFloat(formData.price)) || parseFloat(formData.price) <= 0) {
      errors.price = 'Price must be a positive number';
    }



    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      // Convert string values to appropriate types
      const productData = {
        ...formData,
        price: parseFloat(formData.price),
        isAvailable: formData.isAvailable === 'true' || formData.isAvailable === true
      };

      if (imageFile) {
        // If we have an image file, use the with-image endpoints
        if (isEditMode) {
          await ProductService.updateProductWithImage(id, productData, imageFile);
        } else {
          await ProductService.createProductWithImage(productData, imageFile);
        }
      } else {
        // Otherwise use the regular endpoints
        if (isEditMode) {
          await ProductService.updateProduct(id, productData);
        } else {
          await ProductService.createProduct(productData);
        }
      }

      navigate('/products');
    } catch (err) {
      setError(`Failed to ${isEditMode ? 'update' : 'create'} product. Please try again.`);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  if (loading && isEditMode) {
    return <div className="loading">Loading product details...</div>;
  }

  return (
    <div className="product-form-container">
      <h2>{isEditMode ? 'Edit Product' : 'Add New Product'}</h2>

      {error && <div className="error-message">{error}</div>}

      <form onSubmit={handleSubmit} className="product-form">
        <div className="form-group">
          <label htmlFor="name">Product Name*</label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className={validationErrors.name ? 'error' : ''}
          />
          {validationErrors.name && <div className="error-text">{validationErrors.name}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="altname">Alternative Name</label>
          <input
            type="text"
            id="altname"
            name="altname"
            value={formData.altname}
            onChange={handleChange}
          />
        </div>

        <div className="form-group">
          <label htmlFor="description">Description</label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows="4"
          />
        </div>

        <div className="form-group">
          <label htmlFor="price">Price*</label>
          <input
            type="text"
            id="price"
            name="price"
            value={formData.price}
            onChange={handleChange}
            className={validationErrors.price ? 'error' : ''}
          />
          {validationErrors.price && <div className="error-text">{validationErrors.price}</div>}
        </div>



        <div className="form-group">
          <label htmlFor="category">Category</label>
          <input
            type="text"
            id="category"
            name="category"
            value={formData.category}
            onChange={handleChange}
          />
        </div>

        <div className="form-group">
          <label htmlFor="isAvailable">Availability</label>
          <select
            id="isAvailable"
            name="isAvailable"
            value={formData.isAvailable}
            onChange={handleChange}
            className="form-select"
          >
            <option value={true}>Available</option>
            <option value={false}>Not Available</option>
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="image">Product Image</label>
          <input
            type="file"
            id="image"
            name="image"
            accept="image/*"
            onChange={handleImageChange}
            className="form-control"
          />
          {imagePreview && (
            <div className="image-preview">
              <img src={imagePreview} alt="Product preview" />
            </div>
          )}
        </div>

        <div className="form-actions">
          <button
            type="button"
            onClick={() => navigate('/products')}
            className="btn-cancel"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn-save"
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Product'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProductForm;

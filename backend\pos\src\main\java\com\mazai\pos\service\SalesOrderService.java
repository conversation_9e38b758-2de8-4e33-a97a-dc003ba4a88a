package com.mazai.pos.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mazai.pos.model.Order;
import com.mazai.pos.model.OrderItem;
import com.mazai.pos.model.Product;
import com.mazai.pos.model.User;
import com.mazai.pos.repository.OrderItemRepository;
import com.mazai.pos.repository.OrderRepository;
import com.mazai.pos.repository.ProductRepository;

@Service
public class SalesOrderService {

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private OrderItemRepository orderItemRepository;

    @Autowired
    private ProductRepository productRepository;

    public List<Order> getAllOrders() {
        return orderRepository.findAll();
    }

    public Optional<Order> getOrderById(Long id) {
        return orderRepository.findById(id);
    }

    public List<Order> getOrdersByUser(User user) {
        // This method needs to be implemented in OrderRepository
        // For now, return an empty list
        return List.of();
    }

    public List<Order> getOrdersByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return orderRepository.findByUpdatedAtBetween(startDate, endDate);
    }

    public List<Order> getOrdersByTableNumber(String tableNumber) {
        return orderRepository.findByTableNumber(tableNumber);
    }

    public List<Order> getOrdersByStatus(String status) {
        try {
            // Map old status values to new ones for backward compatibility
            Order.OrderStatus orderStatus = switch (status.toUpperCase()) {
                case "PENDING" -> Order.OrderStatus.OPEN;
                case "PAID", "SERVED", "CANCELLED" -> Order.OrderStatus.CLOSED;
                default -> Order.OrderStatus.valueOf(status.toUpperCase());
            };

            return orderRepository.findByStatus(orderStatus);
        } catch (IllegalArgumentException e) {
            return List.of();
        }
    }

    @Transactional
    public Order createOrder(Order order) {
        try {
            // orderDate字段已删除，使用createdAt和updatedAt

            // Initialize total amount if null
            if (order.getTotalAmount() == null) {
                order.setTotalAmount(BigDecimal.ZERO);
            }

            // Ensure order items list is initialized
            if (order.getOrderItems() == null) {
                order.setOrderItems(new ArrayList<>());
            }

            // Calculate total amount
            BigDecimal total = BigDecimal.ZERO;
            List<OrderItem> processedItems = new ArrayList<>();

            for (OrderItem item : order.getOrderItems()) {
                // Skip null items
                if (item == null || item.getProduct() == null || item.getProduct().getId() == null) {
                    continue;
                }

                // Get current product price
                Optional<Product> productOpt = productRepository.findById(item.getProduct().getId());
                if (productOpt.isPresent()) {
                    Product product = productOpt.get();

                    // Set the full product reference
                    item.setProduct(product);

                    // Set unit price from current product price if not set
                    if (item.getUnitPrice() == null) {
                        item.setUnitPrice(product.getPrice());
                    }

                    // Set quantity if not set
                    if (item.getQuantity() == null) {
                        item.setQuantity(1);
                    }

                    // Calculate subtotal
                    item.calculateSubtotal();

                    // Add to total
                    if (item.getSubtotal() != null) {
                        total = total.add(item.getSubtotal());
                    }

                    // Set order reference
                    item.setOrder(order);

                    // Add to processed items
                    processedItems.add(item);
                }
            }

            // Update order with processed items
            order.setOrderItems(processedItems);
            order.setTotalAmount(total);

            // Save Order (will cascade save OrderItems)
            Order savedOrder = orderRepository.save(order);

            return savedOrder;
        } catch (Exception e) {
            // Log the exception
            System.err.println("Error creating order: " + e.getMessage());
            throw new RuntimeException("Failed to create order: " + e.getMessage(), e);
        }
    }

    @Transactional
    public Order updateOrder(Order order) {
        try {
            // Ensure order exists
            if (order.getId() == null) {
                throw new RuntimeException("Order ID cannot be null for update operation");
            }

            Optional<Order> existingOrderOpt = orderRepository.findById(order.getId());
            if (existingOrderOpt.isEmpty()) {
                throw new RuntimeException("Order with ID " + order.getId() + " not found");
            }

            Order existingOrder = existingOrderOpt.get();

            // Update basic fields (orderDate已删除)

            if (order.getStatus() != null) {
                existingOrder.setStatus(order.getStatus());
            }

            if (order.getTableNumber() != null) {
                existingOrder.setTableNumber(order.getTableNumber());
            }

            if (order.getPaidAt() != null) {
                existingOrder.setPaidAt(order.getPaidAt());
            }

            // Handle order items if provided
            if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
                // Clear existing items and add new ones
                existingOrder.getOrderItems().clear();

                for (OrderItem item : order.getOrderItems()) {
                    if (item == null || item.getProduct() == null || item.getProduct().getId() == null) {
                        continue;
                    }

                    // Get current product
                    Optional<Product> productOpt = productRepository.findById(item.getProduct().getId());
                    if (productOpt.isPresent()) {
                        Product product = productOpt.get();

                        // Create new order item
                        OrderItem newItem = new OrderItem();

                        // Set ID if provided (for existing items)
                        if (item.getId() != null) {
                            newItem.setId(item.getId());
                        }

                        newItem.setProduct(product);
                        Integer quantity = item.getQuantity();
                        if (quantity != null) {
                            newItem.setQuantity(quantity);
                        } else {
                            newItem.setQuantity(1);
                        }
                        newItem.setUnitPrice(item.getUnitPrice() != null ? item.getUnitPrice() : product.getPrice());
                        newItem.setVariantIds(item.getVariantIds());
                        Boolean isServed = item.getIsServed();
                        newItem.setIsServed(isServed != null && isServed);

                        // Calculate subtotal
                        newItem.calculateSubtotal();

                        // Add to order
                        existingOrder.addOrderItem(newItem);
                    }
                }
            }

            // Recalculate total
            existingOrder.calculateTotal();

            // Save updated order
            return orderRepository.save(existingOrder);
        } catch (IllegalArgumentException e) {
            System.err.println("Invalid order data: " + e.getMessage());
            throw new RuntimeException("Invalid order data: " + e.getMessage(), e);
        } catch (org.springframework.dao.DataAccessException e) {
            System.err.println("Database error updating order: " + e.getMessage());
            throw new RuntimeException("Database error updating order: " + e.getMessage(), e);
        } catch (Exception e) {
            System.err.println("Unexpected error updating order: " + e.getMessage());
            throw new RuntimeException("Failed to update order: " + e.getMessage(), e);
        }
    }

    public void deleteOrder(Long id) {
        orderRepository.deleteById(id);
    }

    public List<OrderItem> getOrderItems(Long orderId) {
        Optional<Order> orderOpt = orderRepository.findById(orderId);
        if (orderOpt.isPresent()) {
            return orderItemRepository.findByOrder(orderOpt.get());
        }
        return List.of();
    }

    @Transactional
    public OrderItem updateOrderItemPaidStatus(Long itemId, boolean isPaid) {
        Optional<OrderItem> itemOpt = orderItemRepository.findById(itemId);
        if (itemOpt.isPresent()) {
            OrderItem item = itemOpt.get();
            item.setIsPaid(isPaid);
            return orderItemRepository.save(item);
        }
        throw new RuntimeException("Order item not found with id: " + itemId);
    }

    /**
     * 删除订单项
     * @param itemId 订单项ID
     * @return 更新后的订单
     */
    @Transactional
    public Order deleteOrderItem(Long itemId) {
        Optional<OrderItem> itemOpt = orderItemRepository.findById(itemId);
        if (itemOpt.isPresent()) {
            OrderItem item = itemOpt.get();
            Order order = item.getOrder();

            // 从订单中移除订单项
            order.removeOrderItem(item);

            // 删除订单项
            orderItemRepository.delete(item);

            // 重新计算订单总金额
            order.calculateTotal();

            // 保存并返回更新后的订单
            return orderRepository.save(order);
        }
        throw new RuntimeException("Order item not found with id: " + itemId);
    }

    /**
     * 添加新的订单项到现有订单，不影响现有订单项
     * @param orderId 订单ID
     * @param newItems 新的订单项列表
     * @return 更新后的订单
     */
    @Transactional
    public Order addOrderItems(Long orderId, List<OrderItem> newItems) {
        try {
            // 确保订单存在
            Optional<Order> existingOrderOpt = orderRepository.findById(orderId);
            if (existingOrderOpt.isEmpty()) {
                throw new RuntimeException("Order with ID " + orderId + " not found");
            }

            Order existingOrder = existingOrderOpt.get();

            // 处理新的订单项
            for (OrderItem item : newItems) {
                if (item == null || item.getProduct() == null || item.getProduct().getId() == null) {
                    continue;
                }

                // 获取产品信息
                Optional<Product> productOpt = productRepository.findById(item.getProduct().getId());
                if (productOpt.isPresent()) {
                    Product product = productOpt.get();

                    // 创建新的订单项
                    OrderItem newItem = new OrderItem();

                    newItem.setProduct(product);
                    Integer quantity = item.getQuantity();
                    if (quantity != null) {
                        newItem.setQuantity(quantity);
                    } else {
                        newItem.setQuantity(1);
                    }
                    newItem.setUnitPrice(item.getUnitPrice() != null ? item.getUnitPrice() : product.getPrice());
                    newItem.setVariantIds(item.getVariantIds());
                    Boolean isServed = item.getIsServed();
                    newItem.setIsServed(isServed != null && isServed);
                    newItem.setRemark(item.getRemark()); // 确保设置备注

                    // 计算小计
                    newItem.calculateSubtotal();

                    // 添加到订单
                    existingOrder.addOrderItem(newItem);
                }
            }

            // 重新计算总金额
            existingOrder.calculateTotal();

            // 保存更新后的订单
            return orderRepository.save(existingOrder);
        } catch (IllegalArgumentException e) {
            System.err.println("Invalid order item data: " + e.getMessage());
            throw new RuntimeException("Invalid order item data: " + e.getMessage(), e);
        } catch (org.springframework.dao.DataAccessException e) {
            System.err.println("Database error adding order items: " + e.getMessage());
            throw new RuntimeException("Database error adding order items: " + e.getMessage(), e);
        } catch (Exception e) {
            System.err.println("Unexpected error adding order items: " + e.getMessage());
            throw new RuntimeException("Failed to add order items: " + e.getMessage(), e);
        }
    }
}
.order-list-container {
  padding: 20px;
}

.order-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.btn-add {
  background-color: #4CAF50;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
}

.btn-add:hover {
  background-color: #45a049;
}

.filters-container {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-weight: bold;
}

.filter-group select, .filter-group input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.filter-button, .reset-button {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.filter-button {
  background-color: #2196F3;
  color: white;
}

.reset-button {
  background-color: #f2f2f2;
  color: #333;
  margin-left: auto;
}

.filter-button:hover {
  background-color: #0b7dda;
}

.reset-button:hover {
  background-color: #e0e0e0;
}

.table-responsive {
  overflow-x: auto;
}

.order-table {
  width: 100%;
  border-collapse: collapse;
}

.order-table th, .order-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}

.order-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.order-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.order-table tr:hover {
  background-color: #f1f1f1;
}

.status-badge {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  color: white;
}

.status-badge.pending, .status-badge.open {
  background-color: #FFC107;
}

.status-badge.completed, .status-badge.closed {
  background-color: #4CAF50;
}

.status-badge.cancelled {
  background-color: #F44336;
}

/* Additional status styles */
.status-badge.paid {
  background-color: #4CAF50;
}

.status-badge.served {
  background-color: #5bc0de;
}

.actions {
  display: flex;
  gap: 5px;
}

.btn-view, .btn-edit, .btn-delete {
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: white;
}

.btn-view {
  background-color: #2196F3;
}

.btn-edit {
  background-color: #FFC107;
}

.btn-delete {
  background-color: #F44336;
}

.btn-view:hover {
  background-color: #0b7dda;
}

.btn-edit:hover {
  background-color: #e0a800;
}

.btn-delete:hover {
  background-color: #da190b;
}

.loading, .error, .no-orders {
  padding: 20px;
  text-align: center;
}

.error {
  color: #F44336;
}

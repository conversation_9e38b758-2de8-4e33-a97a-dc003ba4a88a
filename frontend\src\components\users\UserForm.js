import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import UserService from '../../services/userService';
import './UserForm.css';

const UserForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditMode = !!id;

  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    role: 'USER',
    active: true
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formErrors, setFormErrors] = useState({});

  useEffect(() => {
    if (isEditMode) {
      fetchUser();
    }
  }, [id]);

  const fetchUser = async () => {
    try {
      setLoading(true);
      const userData = await UserService.getUserById(id);
      // Remove password as it shouldn't be pre-filled
      const { password, ...userDataWithoutPassword } = userData;
      setFormData(userDataWithoutPassword);
    } catch (err) {
      setError('Failed to fetch user details. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });

    // Clear error for this field when user starts typing
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: null
      });
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.username.trim()) {
      errors.username = 'Username is required';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
    }

    if (!isEditMode && !formData.password) {
      errors.password = 'Password is required';
    } else if (!isEditMode && formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      if (isEditMode) {
        // If password is empty in edit mode, remove it from the request
        const dataToSubmit = formData.password
          ? formData
          : { ...formData, password: undefined };

        await UserService.updateUser(id, dataToSubmit);
      } else {
        await UserService.createUser(formData);
      }

      navigate('/users');
    } catch (err) {
      if (err.response && err.response.status === 409) {
        setError('Username or email already exists. Please use different credentials.');
      } else {
        setError(`Failed to ${isEditMode ? 'update' : 'create'} user. Please try again.`);
      }
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  if (loading && isEditMode) return <div className="loading">Loading user data...</div>;

  return (
    <div className="user-form-container">
      <h2>{isEditMode ? 'Edit User' : 'Add New User'}</h2>

      {error && <div className="error-message">{error}</div>}

      <form onSubmit={handleSubmit} className="user-form">
        <div className="form-group">
          <label htmlFor="username">Username</label>
          <input
            type="text"
            id="username"
            name="username"
            value={formData.username}
            onChange={handleChange}
            className={formErrors.username ? 'error' : ''}
          />
          {formErrors.username && <div className="field-error">{formErrors.username}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="email">Email</label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className={formErrors.email ? 'error' : ''}
          />
          {formErrors.email && <div className="field-error">{formErrors.email}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="password">
            Password {isEditMode && <span className="optional">(Leave blank to keep current password)</span>}
          </label>
          <input
            type="password"
            id="password"
            name="password"
            value={formData.password || ''}
            onChange={handleChange}
            className={formErrors.password ? 'error' : ''}
          />
          {formErrors.password && <div className="field-error">{formErrors.password}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="role">Role</label>
          <select
            id="role"
            name="role"
            value={formData.role}
            onChange={handleChange}
          >
            <option value="USER">User (Read-only access)</option>
            <option value="ADMIN">Admin (Full access)</option>
          </select>
          <small className="role-description">
            {formData.role === 'ADMIN'
              ? 'Admin users can create, edit, and delete all data.'
              : 'User role provides read-only access to view data only.'}
          </small>
        </div>

        <div className="form-group checkbox-group">
          <label>
            <input
              type="checkbox"
              name="active"
              checked={formData.active}
              onChange={handleChange}
            />
            Active
          </label>
        </div>

        <div className="form-actions">
          <button
            type="button"
            className="btn-cancel"
            onClick={() => navigate('/users')}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn-submit"
            disabled={loading}
          >
            {loading ? 'Saving...' : isEditMode ? 'Update User' : 'Create User'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default UserForm;
package com.mazai.pos.model;

/**
 * 打印任务类
 * 用于区分不同类型的打印任务
 */
public class PrintTask {

    public enum PrintType {
        RECEIPT,        // 收据打印（带价格）
        KITCHEN_ORDER   // 厨房订单打印（不带价格）
    }

    private Order order;
    private PrintType printType;
    private String receiptType; // 收据类型 (CHINESE/MALAY)
    private long createdAt;

    public PrintTask(Order order, PrintType printType) {
        this.order = order;
        this.printType = printType;
        this.receiptType = "CHINESE"; // 默认华语
        this.createdAt = System.currentTimeMillis();
    }

    public PrintTask(Order order, PrintType printType, String receiptType) {
        this.order = order;
        this.printType = printType;
        this.receiptType = receiptType != null ? receiptType : "CHINESE";
        this.createdAt = System.currentTimeMillis();
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public PrintType getPrintType() {
        return printType;
    }

    public void setPrintType(PrintType printType) {
        this.printType = printType;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public String getReceiptType() {
        return receiptType;
    }

    public void setReceiptType(String receiptType) {
        this.receiptType = receiptType != null ? receiptType : "CHINESE";
    }

    @Override
    public String toString() {
        return String.format("PrintTask{orderId=%d, type=%s, receiptType=%s, createdAt=%d}",
            order != null ? order.getId() : null, printType, receiptType, createdAt);
    }
}

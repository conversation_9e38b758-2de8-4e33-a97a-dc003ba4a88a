package com.mazai.pos.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mazai.pos.model.Order;
import com.mazai.pos.model.OrderItem;
import com.mazai.pos.model.Payment;
import com.mazai.pos.model.PaymentItem;
import com.mazai.pos.repository.OrderRepository;
import com.mazai.pos.repository.PaymentItemRepository;
import com.mazai.pos.repository.PaymentRepository;

@Service
public class PaymentService {

    @Autowired
    private PaymentRepository paymentRepository;

    @Autowired
    private PaymentItemRepository paymentItemRepository;

    @Autowired
    private OrderRepository orderRepository;

    public List<Payment> getAllPayments() {
        return paymentRepository.findAll();
    }

    public Optional<Payment> getPaymentById(Long id) {
        return paymentRepository.findById(id);
    }

    public List<Payment> getPaymentsByOrder(Order order) {
        return paymentRepository.findByOrder(order);
    }

    @Transactional
    public Payment createPayment(Payment payment) {
        // Save the payment
        Payment savedPayment = paymentRepository.save(payment);

        // Set payment reference in payment items and save them
        for (PaymentItem item : payment.getPaymentItems()) {
            item.setPayment(savedPayment);
            paymentItemRepository.save(item);
        }

        // 获取订单，但不自动更改其状态
        // 订单状态应该由调用方决定
        Order order = payment.getOrder();

        // 如果订单状态已经是CLOSED，则更新支付时间
        if (order.getStatus() == Order.OrderStatus.CLOSED && order.getPaidAt() == null) {
            order.setPaidAt(LocalDateTime.now());
            orderRepository.save(order);
        } else {
            // 否则只保存订单，不更改其状态
            orderRepository.save(order);
        }

        return savedPayment;
    }

    @Transactional
    public Payment processOrderPayment(Long orderId, String paymentMethod, boolean issueReceipt) {
        return processOrderPayment(orderId, paymentMethod, issueReceipt, null);
    }

    @Transactional
    public Payment processOrderPayment(Long orderId, String paymentMethod, boolean issueReceipt, BigDecimal amountPaid) {
        // Find the order
        Optional<Order> orderOpt = orderRepository.findById(orderId);
        if (!orderOpt.isPresent()) {
            throw new RuntimeException("Order not found with ID: " + orderId);
        }

        Order order = orderOpt.get();

        // Create a new payment
        Payment payment = new Payment();
        payment.setOrder(order);
        payment.setPaymentMethod(paymentMethod);
        payment.setAmount(order.getTotalAmount());
        payment.setReceiptIssued(issueReceipt);

        // 设置实际支付金额和找零
        if (amountPaid != null) {
            payment.setPaid(amountPaid);
            // 计算找零：实际支付金额 - 订单金额
            BigDecimal change = amountPaid.subtract(order.getTotalAmount());
            payment.setChangeAmount(change.compareTo(BigDecimal.ZERO) > 0 ? change : BigDecimal.ZERO);
        } else {
            // 如果没有提供实际支付金额，假设精确支付
            payment.setPaid(order.getTotalAmount());
            payment.setChangeAmount(BigDecimal.ZERO);
        }

        // Create payment items for each order item
        for (OrderItem orderItem : order.getOrderItems()) {
            PaymentItem paymentItem = new PaymentItem();
            paymentItem.setOrderItem(orderItem);
            payment.addPaymentItem(paymentItem);

            // Mark the order item as paid
            orderItem.setIsPaid(true);
        }

        // 全部支付时，将订单状态设置为已关闭
        // 因为这是完整支付，所以所有项目都已支付
        order.setStatus(Order.OrderStatus.CLOSED);
        order.setPaidAt(LocalDateTime.now());
        orderRepository.save(order);

        // Save the payment and its items
        return paymentRepository.save(payment);
    }

    @Transactional
    public void deletePayment(Long id) {
        paymentRepository.deleteById(id);
    }
}

package com.mazai.pos.security.jwt;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.security.Keys;

@Component
public class JwtUtils {
    private static final Logger logger = LoggerFactory.getLogger(JwtUtils.class);

    @Value("${pos.app.jwtSecret:defaultSecretKeyWhichShouldBeChangedInProduction}")
    private String jwtSecret;

    @Value("${pos.app.jwtExpirationMs:86400000}")
    private int jwtExpirationMs;

    private Key key;

    public Key getSigningKey() {
        if (key == null) {
            key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));
            logger.info("Using configured key for HS512 algorithm");
        }
        return key;
    }

    public String generateJwtToken(Authentication authentication) {
        UserDetails userPrincipal = (UserDetails) authentication.getPrincipal();

        logger.debug("Generating JWT token for user: {}", userPrincipal.getUsername());
        logger.debug("JWT expiration: {} ms", jwtExpirationMs);

        String token = Jwts.builder()
                .setSubject(userPrincipal.getUsername())
                .setIssuedAt(new Date())
                .setExpiration(new Date((new Date()).getTime() + jwtExpirationMs))
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();

        logger.debug("Generated JWT token: {}", token);
        return token;
    }

    public String getUserNameFromJwtToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody()
                .getSubject();
    }

    public boolean validateJwtToken(String authToken) {
        logger.debug("Validating JWT token, length: {}", authToken.length());
        logger.debug("JWT token first 20 chars: {}", authToken.substring(0, Math.min(20, authToken.length())) + "...");

        try {
            // 解析JWT结构
            String[] parts = authToken.split("\\.");
            if (parts.length != 3) {
                logger.error("Invalid JWT token structure: does not have 3 parts");
                return false;
            }

            logger.debug("JWT structure appears valid (has 3 parts)");

            try {
                // 解析JWT头部和载荷（不解码签名）
                String header = new String(java.util.Base64.getDecoder().decode(parts[0]));
                String payload = new String(java.util.Base64.getDecoder().decode(parts[1]));
                logger.debug("JWT header: {}", header);
                logger.debug("JWT payload: {}", payload);

                // 解析过期时间 - 使用简单的字符串解析而不是依赖org.json
                if (payload.contains("\"exp\":")) {
                    try {
                        // 简单解析exp值
                        int expIndex = payload.indexOf("\"exp\":");
                        int colonIndex = payload.indexOf(":", expIndex);
                        int commaIndex = payload.indexOf(",", colonIndex);
                        if (commaIndex == -1) {
                            commaIndex = payload.indexOf("}", colonIndex);
                        }

                        String expValue = payload.substring(colonIndex + 1, commaIndex).trim();
                        long expTime = Long.parseLong(expValue);
                        long currentTime = System.currentTimeMillis() / 1000;

                        logger.debug("JWT expiration time: {}, current time: {}, diff: {} seconds",
                                    expTime, currentTime, (expTime - currentTime));

                        if (expTime < currentTime) {
                            logger.error("JWT token is expired: exp={}, now={}", expTime, currentTime);
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("Error parsing expiration time: {}", e.getMessage());
                    }
                }
            } catch (Exception e) {
                logger.error("Error parsing JWT parts: {}", e.getMessage());
                // 继续验证，因为这只是额外的日志记录
            }

            // 使用JWT库进行完整验证
            Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(authToken);

            logger.debug("JWT token is valid");
            return true;
        } catch (MalformedJwtException e) {
            logger.error("Invalid JWT token: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            logger.error("JWT token is expired: {}", e.getMessage());
            logger.error("Expired JWT details - issued: {}, expiration: {}, current time: {}",
                        e.getClaims().getIssuedAt(),
                        e.getClaims().getExpiration(),
                        new java.util.Date());
        } catch (UnsupportedJwtException e) {
            logger.error("JWT token is unsupported: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            logger.error("JWT claims string is empty: {}", e.getMessage());
        } catch (Exception e) {
            logger.error("JWT validation error: {}", e.getMessage(), e);
        }

        logger.debug("JWT token is invalid");
        return false;
    }
}






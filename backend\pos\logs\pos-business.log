2025-06-14 21:50:43.706 [http-nio-8080-exec-1] INFO  c.m.pos.controller.AuthController - User admin logged in successfully
2025-06-14 21:51:46.194 [http-nio-8080-exec-2] INFO  c.m.pos.controller.ProductController - Creating new product: Cham
2025-06-14 21:51:46.237 [http-nio-8080-exec-2] INFO  c.m.pos.controller.ProductController - Product created successfully with ID: 51
2025-06-14 22:14:58.877 [http-nio-8080-exec-1] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:14:58.878 [http-nio-8080-exec-1] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749910498878}
2025-06-14 22:15:52.233 [http-nio-8080-exec-6] INFO  c.m.p.c.MobileOrderController - Creating order with 5 items
2025-06-14 22:15:52.306 [http-nio-8080-exec-6] INFO  c.m.p.c.MobileOrderController - Order #2 queued for kitchen printing
2025-06-14 22:15:52.307 [http-nio-8080-exec-6] INFO  c.m.p.c.MobileOrderController - Order created successfully with ID: 2
2025-06-14 22:17:31.016 [http-nio-8080-exec-1] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:17:31.017 [http-nio-8080-exec-1] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749910651017}
2025-06-14 22:17:46.792 [http-nio-8080-exec-3] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:17:46.792 [http-nio-8080-exec-3] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749910666792}
2025-06-14 22:18:59.953 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:18:59.953 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749910739953}
2025-06-14 22:19:15.083 [http-nio-8080-exec-2] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:19:15.084 [http-nio-8080-exec-2] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749910755084}
2025-06-14 22:20:07.383 [http-nio-8080-exec-4] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:20:07.383 [http-nio-8080-exec-4] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749910807383}
2025-06-14 22:32:27.668 [http-nio-8080-exec-9] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:32:27.668 [http-nio-8080-exec-9] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749911547668}
2025-06-14 22:32:48.397 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:32:48.397 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749911568397}
2025-06-14 22:33:02.236 [http-nio-8080-exec-5] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:33:02.237 [http-nio-8080-exec-5] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749911582237}
2025-06-14 22:43:24.274 [http-nio-8080-exec-4] INFO  c.m.pos.controller.ProductController - Creating new product: 星洲米粉
2025-06-14 22:43:24.282 [http-nio-8080-exec-4] INFO  c.m.pos.controller.ProductController - Product created successfully with ID: 52
2025-06-14 22:44:01.315 [http-nio-8080-exec-6] INFO  c.m.pos.controller.ProductController - Updating product with ID: 52
2025-06-14 22:44:01.327 [http-nio-8080-exec-6] INFO  c.m.pos.controller.ProductController - Product updated successfully: 星洲米粉
2025-06-14 22:56:39.960 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 收到健康检查请求
2025-06-14 22:56:39.961 [http-nio-8080-exec-7] INFO  c.m.p.c.MobileHealthController - MobileHealthController: 返回健康检查结果: {message=Server is running, status=UP, timestamp=1749912999961}

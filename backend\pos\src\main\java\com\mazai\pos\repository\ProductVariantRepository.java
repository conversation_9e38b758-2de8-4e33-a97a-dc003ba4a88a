package com.mazai.pos.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.mazai.pos.model.Product;
import com.mazai.pos.model.ProductVariant;

@Repository
public interface ProductVariantRepository extends JpaRepository<ProductVariant, Long> {
    List<ProductVariant> findByProduct(Product product);
    List<ProductVariant> findByProductAndVariantType(Product product, String variantType);
    List<ProductVariant> findByProductAndIsDefaultTrue(Product product);
}

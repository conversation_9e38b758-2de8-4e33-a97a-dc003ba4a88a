package com.mazai.pos.repository;

import com.mazai.pos.model.PaymentItem;
import com.mazai.pos.model.Payment;
import com.mazai.pos.model.OrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PaymentItemRepository extends JpaRepository<PaymentItem, Long> {
    List<PaymentItem> findByPayment(Payment payment);
    List<PaymentItem> findByOrderItem(OrderItem orderItem);
}

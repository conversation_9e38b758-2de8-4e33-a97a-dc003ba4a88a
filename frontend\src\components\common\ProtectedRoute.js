import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading, currentUser } = useAuth();
  const location = useLocation();

  useEffect(() => {
    console.log('ProtectedRoute at', location.pathname);
    console.log('Authentication state:', {
      isAuthenticated,
      loading,
      user: currentUser ? {
        id: currentUser.id,
        username: currentUser.username,
        role: currentUser.role,
        hasToken: !!currentUser.token
      } : null
    });

    // 检查localStorage中的用户信息
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        console.log('User in localStorage:', {
          id: user.id,
          username: user.username,
          role: user.role,
          hasToken: !!user.token
        });
      } else {
        console.log('No user in localStorage');
      }
    } catch (error) {
      console.error('Error checking localStorage:', error);
    }
  }, [isAuthenticated, loading, currentUser, location.pathname]);

  if (loading) {
    console.log('ProtectedRoute: Still loading authentication state');
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    console.log('ProtectedRoute: Not authenticated, redirecting to login');
    console.log('Current path:', location.pathname);

    // 如果用户已经在登录页面，就不要再重定向了
    if (location.pathname === '/login') {
      console.log('Already on login page, not redirecting');
      return children;
    }

    return <Navigate to="/login" />;
  }

  console.log('ProtectedRoute: Authenticated, rendering children for path:', location.pathname);
  return children;
};

export default ProtectedRoute;

import mobileApi from './mobileApi';

const MobileProductVariantService = {
  getAllVariants: async () => {
    try {
      console.log('MobileProductVariantService: Fetching all variants');
      const response = await mobileApi.get('/product-variants');
      console.log('MobileProductVariantService: Variants fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching product variants:', error.response?.status, error.message);
      console.error('Error details:', error.response?.data);
      throw error;
    }
  },

  getVariantById: async (id) => {
    try {
      const response = await mobileApi.get(`/product-variants/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching product variant ${id}:`, error);
      throw error;
    }
  },

  getVariantsByProduct: async (productId) => {
    try {
      const response = await mobileApi.get(`/product-variants/product/${productId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching variants for product ${productId}:`, error);
      throw error;
    }
  },

  getVariantsByProductAndType: async (productId, type) => {
    try {
      const response = await mobileApi.get(`/product-variants/product/${productId}/type/${type}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching variants for product ${productId} and type ${type}:`, error);
      throw error;
    }
  },

  getDefaultVariantsByProduct: async (productId) => {
    try {
      const response = await mobileApi.get(`/product-variants/product/${productId}/default`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching default variants for product ${productId}:`, error);
      throw error;
    }
  }
};

export default MobileProductVariantService;

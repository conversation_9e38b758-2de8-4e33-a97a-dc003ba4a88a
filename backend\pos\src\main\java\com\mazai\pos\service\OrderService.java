package com.mazai.pos.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mazai.pos.model.Order;
import com.mazai.pos.model.OrderItem;
import com.mazai.pos.model.Product;
import com.mazai.pos.repository.OrderItemRepository;
import com.mazai.pos.repository.OrderRepository;
import com.mazai.pos.repository.ProductRepository;

@Service
public class OrderService {

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private OrderItemRepository orderItemRepository;

    @Autowired
    private ProductRepository productRepository;

    public List<Order> getAllOrders() {
        return orderRepository.findAll();
    }

    public Optional<Order> getOrderById(Long id) {
        return orderRepository.findById(id);
    }

    public List<Order> getOrdersByStatus(Order.OrderStatus status) {
        return orderRepository.findByStatus(status);
    }

    public List<Order> getOrdersByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return orderRepository.findByUpdatedAtBetween(startDate, endDate);
    }

    public List<Order> getOrdersByTableNumber(String tableNumber) {
        return orderRepository.findByTableNumber(tableNumber);
    }

    // 为了向后兼容，添加一个接受Integer参数的方法
    public List<Order> getOrdersByTableNumber(Integer tableNumber) {
        return orderRepository.findByTableNumber(tableNumber.toString());
    }

    @Transactional
    public Order createOrder(Order order) {
        // orderDate字段已删除，使用createdAt和updatedAt

        // Calculate total amount
        BigDecimal total = BigDecimal.ZERO;
        for (OrderItem item : order.getOrderItems()) {
            // Get current product price
            Optional<Product> productOpt = productRepository.findById(item.getProduct().getId());
            if (productOpt.isPresent()) {
                Product product = productOpt.get();

                // Check if product is available
                if (!product.getIsAvailable()) {
                    throw new RuntimeException("Product is not available: " + product.getName());
                }

                // Set unit price from current product price
                item.setUnitPrice(product.getPrice());

                // Calculate subtotal
                item.calculateSubtotal();

                // Add to total
                total = total.add(item.getSubtotal());
            }
        }

        order.setTotalAmount(total);

        // 在保存 Order 之前，先设置每个 OrderItem 的 order 引用
        for (OrderItem item : order.getOrderItems()) {
            item.setOrder(order);
        }

        // 保存 Order（会级联保存 OrderItems）
        Order savedOrder = orderRepository.save(order);

        return savedOrder;
    }

    @Transactional
    public Order updateOrder(Order order) {
        // Calculate total amount
        BigDecimal total = BigDecimal.ZERO;
        for (OrderItem item : order.getOrderItems()) {
            if (item.getSubtotal() != null) {
                total = total.add(item.getSubtotal());
            }
        }

        order.setTotalAmount(total);

        return orderRepository.save(order);
    }

    @Transactional
    public Order updateOrderStatus(Long orderId, Order.OrderStatus status) {
        Optional<Order> orderOpt = orderRepository.findById(orderId);
        if (orderOpt.isPresent()) {
            Order order = orderOpt.get();
            order.setStatus(status);

            // If status is CLOSED, set paidAt timestamp
            if (status == Order.OrderStatus.CLOSED) {
                order.setPaidAt(LocalDateTime.now());
            }

            return orderRepository.save(order);
        }
        throw new RuntimeException("Order not found with id: " + orderId);
    }

    @Transactional
    public OrderItem updateOrderItemServedStatus(Long itemId, boolean isServed) {
        Optional<OrderItem> itemOpt = orderItemRepository.findById(itemId);
        if (itemOpt.isPresent()) {
            OrderItem item = itemOpt.get();
            item.setIsServed(isServed);
            return orderItemRepository.save(item);
        }
        throw new RuntimeException("Order item not found with id: " + itemId);
    }

    @Transactional
    public OrderItem updateOrderItemPaidStatus(Long itemId, boolean isPaid) {
        Optional<OrderItem> itemOpt = orderItemRepository.findById(itemId);
        if (itemOpt.isPresent()) {
            OrderItem item = itemOpt.get();
            item.setIsPaid(isPaid);
            return orderItemRepository.save(item);
        }
        throw new RuntimeException("Order item not found with id: " + itemId);
    }

    public void deleteOrder(Long id) {
        orderRepository.deleteById(id);
    }

    public List<OrderItem> getOrderItems(Long orderId) {
        Optional<Order> orderOpt = orderRepository.findById(orderId);
        if (orderOpt.isPresent()) {
            return orderItemRepository.findByOrder(orderOpt.get());
        }
        return List.of();
    }
}
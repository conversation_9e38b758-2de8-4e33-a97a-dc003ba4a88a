import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import ProductVariantService from '../../services/productVariantService';
import ProductService from '../../services/productService';
import { useAuth } from '../../context/AuthContext';
import './ProductVariantForm.css';

const ProductVariantForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { isAuthenticated, currentUser } = useAuth();
  const isEditMode = !!id;

  const [formData, setFormData] = useState({
    productId: '',
    name: '',
    variantType: 'SIZE',
    priceAdjustment: '',
    isDefault: false
  });

  // 移除额外的认证检查，因为ProtectedRoute已经处理了认证

  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});

  // 添加认证状态检查
  useEffect(() => {
    console.log('ProductVariantForm: Authentication state check');
    console.log('isAuthenticated:', isAuthenticated);
    console.log('currentUser:', currentUser);

    if (!isAuthenticated) {
      console.log('ProductVariantForm: User not authenticated, redirecting to login');
      setError('Authentication required. Please log in.');
      navigate('/login');
      return;
    }

    // 检查localStorage中的用户信息
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        console.log('ProductVariantForm: User found in localStorage:', {
          id: user.id,
          username: user.username,
          role: user.role,
          hasToken: !!user.token
        });

        // 确保令牌存在
        if (!user.token) {
          console.log('ProductVariantForm: No token in user object');
          setError('Authentication token missing. Please log in again.');
          navigate('/login');
          return;
        }
      } else {
        console.log('ProductVariantForm: No user found in localStorage');
        setError('Authentication required. Please log in.');
        navigate('/login');
        return;
      }
    } catch (error) {
      console.error('ProductVariantForm: Error checking localStorage:', error);
      setError('Authentication error. Please log in again.');
      navigate('/login');
      return;
    }

    fetchProducts();

    if (isEditMode) {
      fetchVariant();
    }
  }, [isAuthenticated, currentUser, id, isEditMode, navigate]);

  const fetchProducts = async () => {
    try {
      const data = await ProductService.getAllProducts();
      setProducts(data);

      // If not in edit mode and products are loaded, set the first product as default
      if (!isEditMode && data.length > 0 && !formData.productId) {
        setFormData(prev => ({
          ...prev,
          productId: data[0].id.toString()
        }));
      }
    } catch (err) {
      console.error('Failed to fetch products:', err);
      setError('Failed to load products. Please try again.');
    }
  };

  const fetchVariant = async () => {
    try {
      setLoading(true);
      const data = await ProductVariantService.getVariantById(id);

      setFormData({
        productId: data.product.id.toString(),
        name: data.name,
        variantType: data.variantType,
        priceAdjustment: data.priceAdjustment.toString(),
        isDefault: data.isDefault
      });

      setError(null);
    } catch (err) {
      setError('Failed to fetch variant details. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });

    // Clear validation error when field is edited
    if (validationErrors[name]) {
      setValidationErrors({
        ...validationErrors,
        [name]: null
      });
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.productId) {
      errors.productId = 'Please select a product';
    }

    if (!formData.name.trim()) {
      errors.name = 'Variant name is required';
    }

    if (!formData.variantType) {
      errors.variantType = 'Variant type is required';
    }

    if (!formData.priceAdjustment.trim()) {
      errors.priceAdjustment = 'Price adjustment is required';
    } else if (isNaN(parseFloat(formData.priceAdjustment))) {
      errors.priceAdjustment = 'Price adjustment must be a number';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('ProductVariantForm: Form submitted');

    if (!validateForm()) {
      console.log('ProductVariantForm: Form validation failed');
      return;
    }

    // 检查认证状态
    if (!isAuthenticated || !currentUser) {
      console.log('ProductVariantForm: Not authenticated, cannot submit form');
      setError('Authentication required. Please log in again.');
      navigate('/login');
      return;
    }

    // 检查localStorage中的用户信息和令牌
    try {
      const userStr = localStorage.getItem('user');
      if (!userStr) {
        console.log('ProductVariantForm: No user in localStorage, cannot submit form');
        setError('Authentication required. Please log in again.');
        navigate('/login');
        return;
      }

      const user = JSON.parse(userStr);
      if (!user.token) {
        console.log('ProductVariantForm: No token in user object, cannot submit form');
        setError('Authentication token missing. Please log in again.');
        navigate('/login');
        return;
      }
    } catch (error) {
      console.error('ProductVariantForm: Error checking localStorage:', error);
      setError('Authentication error. Please log in again.');
      navigate('/login');
      return;
    }

    try {
      setLoading(true);
      console.log('ProductVariantForm: Processing form data...');

      // Convert string values to appropriate types
      const variantData = {
        product: { id: parseInt(formData.productId) },
        name: formData.name,
        variantType: formData.variantType,
        priceAdjustment: parseFloat(formData.priceAdjustment),
        isDefault: formData.isDefault
      };

      console.log('ProductVariantForm: Prepared variant data:', variantData);

      if (isEditMode) {
        console.log(`ProductVariantForm: Updating variant with ID ${id}`);
        await ProductVariantService.updateVariant(id, variantData);
        console.log('ProductVariantForm: Variant updated successfully');
      } else {
        console.log('ProductVariantForm: Creating new variant');
        const result = await ProductVariantService.createVariant(variantData);
        console.log('ProductVariantForm: Variant created successfully:', result);
      }

      console.log('ProductVariantForm: Navigating to variant list');
      navigate('/product-variants');
    } catch (err) {
      console.error('ProductVariantForm: Error submitting form:', err);

      // 检查是否是认证错误
      if (err.response && err.response.status === 401) {
        console.error('ProductVariantForm: Authentication error (401)');
        setError('Authentication error. Please log in again.');

        // 不要立即导航，让用户看到错误消息
        setTimeout(() => {
          navigate('/login');
        }, 2000);
      } else {
        setError(`Failed to ${isEditMode ? 'update' : 'create'} variant. ${err.message || 'Please try again.'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  if (loading && isEditMode) {
    return <div className="loading">Loading variant details...</div>;
  }

  return (
    <div className="variant-form-container">
      <h2>{isEditMode ? 'Edit Product Variant' : 'Add New Product Variant'}</h2>

      {error && <div className="error-message">{error}</div>}

      <form onSubmit={handleSubmit} className="variant-form">
        <div className="form-group">
          <label htmlFor="productId">Product*</label>
          <select
            id="productId"
            name="productId"
            value={formData.productId}
            onChange={handleChange}
            className={validationErrors.productId ? 'error' : ''}
          >
            <option value="">Select a product</option>
            {products.map(product => (
              <option key={product.id} value={product.id.toString()}>
                {product.name}
              </option>
            ))}
          </select>
          {validationErrors.productId && <div className="error-text">{validationErrors.productId}</div>}
        </div>

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="name">Variant Name*</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className={validationErrors.name ? 'error' : ''}
              placeholder="e.g., Large, Extra Cheese"
            />
            {validationErrors.name && <div className="error-text">{validationErrors.name}</div>}
          </div>

          <div className="form-group">
            <label htmlFor="variantType">Variant Type*</label>
            <select
              id="variantType"
              name="variantType"
              value={formData.variantType}
              onChange={handleChange}
              className={validationErrors.variantType ? 'error' : ''}
            >
              <option value="SIZE">Size</option>
              <option value="ADDON">Add-on</option>
              <option value="PACKAGING">Packaging</option>
              <option value="NOODLE">Noodle</option>
              <option value="TEA">Tea</option>
              

            </select>
            {validationErrors.variantType && <div className="error-text">{validationErrors.variantType}</div>}
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="priceAdjustment">Price Adjustment (RM)*</label>
          <input
            type="text"
            id="priceAdjustment"
            name="priceAdjustment"
            value={formData.priceAdjustment}
            onChange={handleChange}
            className={validationErrors.priceAdjustment ? 'error' : ''}
            placeholder="e.g., 2.50"
          />
          {validationErrors.priceAdjustment && <div className="error-text">{validationErrors.priceAdjustment}</div>}
          <small>Enter positive value to increase price, negative to decrease.</small>
        </div>

        <div className="form-group">
          <div className="checkbox-group">
            <input
              type="checkbox"
              id="isDefault"
              name="isDefault"
              checked={formData.isDefault}
              onChange={handleChange}
            />
            <label htmlFor="isDefault">Set as default variant</label>
          </div>
        </div>

        <div className="form-actions">
          <button
            type="button"
            onClick={() => navigate('/product-variants')}
            className="btn-cancel"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn-save"
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Variant'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProductVariantForm;

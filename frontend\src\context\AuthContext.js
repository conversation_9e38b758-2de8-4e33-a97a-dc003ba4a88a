import React, { createContext, useState, useEffect, useContext } from 'react';
import AuthService from '../services/authService';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    console.log('AuthContext initializing');

    try {
      const user = AuthService.getCurrentUser();

      if (user) {
        console.log('User found in localStorage:', JSON.stringify(user, null, 2));

        // Check if token exists
        if (user.token) {
          console.log('Token found in user object, setting authenticated state');

          // Set the token in axios default headers
          const token = user.token;
          console.log('Setting default Authorization header with token');
          import('../services/api').then(apiModule => {
            const api = apiModule.default;
            api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
            console.log('Default Authorization header set');
          });

          setCurrentUser(user);
          setIsAuthenticated(true);
        } else {
          console.log('No token found in user object, not setting authenticated state');
          setCurrentUser(null);
          setIsAuthenticated(false);
          // Clear invalid user data
          AuthService.logout();
        }
      } else {
        console.log('No user found in localStorage');
        setCurrentUser(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Error initializing auth context:', error);
      setCurrentUser(null);
      setIsAuthenticated(false);
      // Clear potentially corrupted user data
      AuthService.logout();
    } finally {
      setLoading(false);
    }
  }, []);

  const login = async (username, password) => {
    try {
      console.log('AuthContext: Attempting login for user:', username);
      setLoading(true);

      const response = await AuthService.login(username, password);
      console.log('AuthContext: Login successful, response:', JSON.stringify(response, null, 2));

      // Verify token exists in response
      if (!response.token && !response.accessToken) {
        console.error('AuthContext: No token in login response');
        throw new Error('No authentication token received from server');
      }

      // Use token from response
      const token = response.token || response.accessToken;
      console.log('AuthContext: Token received, length:', token.length);

      // Set the token in axios default headers
      const apiModule = await import('../services/api');
      const api = apiModule.default;
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      console.log('AuthContext: Default Authorization header set');

      // 验证认证状态
      try {
        const authStatus = await AuthService.checkAuthStatus();
        console.log('AuthContext: Server auth status check:', authStatus);

        if (!authStatus.isAuthenticated) {
          console.error('AuthContext: Server reports not authenticated despite token');
          throw new Error('Server authentication failed');
        }
      } catch (statusError) {
        console.error('AuthContext: Error checking auth status:', statusError);
        // 继续执行，不要中断登录流程
      }

      // Update state
      console.log('AuthContext: Setting currentUser and isAuthenticated state');
      setCurrentUser(response);
      setIsAuthenticated(true);

      // 验证状态是否已更新
      console.log('AuthContext: State should be updated now');
      console.log('AuthContext: Updated state (note: may not reflect latest due to React state update timing):', {
        isAuthenticated: true,
        currentUser: {
          id: response.id,
          username: response.username,
          role: response.role,
          hasToken: !!response.token
        }
      });

      // 强制记录一下localStorage中的用户信息
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          const user = JSON.parse(userStr);
          console.log('AuthContext: User in localStorage after login:', {
            id: user.id,
            username: user.username,
            role: user.role,
            hasToken: !!user.token
          });
        } else {
          console.log('AuthContext: No user in localStorage after login - this is unexpected!');
        }
      } catch (error) {
        console.error('AuthContext: Error checking localStorage after login:', error);
      }

      return response;
    } catch (error) {
      console.error('AuthContext: Login failed:', error);
      console.error('AuthContext: Error details:', error.response?.data);

      // Clear any partial state
      setCurrentUser(null);
      setIsAuthenticated(false);

      // Clear localStorage in case of error
      AuthService.logout();

      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    AuthService.logout();
    setCurrentUser(null);
    setIsAuthenticated(false);
  };

  const register = async (username, email, password, role) => {
    return AuthService.register(username, email, password, role);
  };

  const value = {
    currentUser,
    loading,
    login,
    logout,
    register,
    isAuthenticated
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

export default AuthContext;


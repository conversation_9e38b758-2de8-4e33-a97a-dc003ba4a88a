package com.mazai.pos.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mazai.pos.model.User;
import com.mazai.pos.payload.response.MessageResponse;
import com.mazai.pos.repository.UserRepository;


@RestController
@RequestMapping("/api/admin")
public class PasswordResetController {

    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(PasswordResetController.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @PostMapping("/reset-password/{userId}")
    public ResponseEntity<?> resetUserPassword(@PathVariable Long userId, @RequestBody Map<String, String> request) {
        try {
            String newPassword = request.get("newPassword");

            if (newPassword == null || newPassword.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new MessageResponse("New password is required"));
            }

            Optional<User> userOptional = userRepository.findById(userId);
            if (!userOptional.isPresent()) {
                return ResponseEntity.badRequest()
                    .body(new MessageResponse("User not found"));
            }

            User user = userOptional.get();

            // Encode the new password
            String encodedPassword = passwordEncoder.encode(newPassword);
            user.setPassword(encodedPassword);

            userRepository.save(user);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Password reset successfully for user: " + user.getUsername());
            response.put("username", user.getUsername());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new MessageResponse("Failed to reset password: " + e.getMessage()));
        }
    }

    @PostMapping("/reset-password-by-username")
    public ResponseEntity<?> resetPasswordByUsername(@RequestBody Map<String, String> request) {
        try {
            String username = request.get("username");
            String newPassword = request.get("newPassword");

            if (username == null || username.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new MessageResponse("Username is required"));
            }

            if (newPassword == null || newPassword.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new MessageResponse("New password is required"));
            }

            Optional<User> userOptional = userRepository.findByUsername(username);
            if (!userOptional.isPresent()) {
                return ResponseEntity.badRequest()
                    .body(new MessageResponse("User not found: " + username));
            }

            User user = userOptional.get();

            // Encode the new password
            String encodedPassword = passwordEncoder.encode(newPassword);
            user.setPassword(encodedPassword);

            userRepository.save(user);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Password reset successfully for user: " + user.getUsername());
            response.put("userId", user.getId());
            response.put("username", user.getUsername());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new MessageResponse("Failed to reset password: " + e.getMessage()));
        }
    }
}

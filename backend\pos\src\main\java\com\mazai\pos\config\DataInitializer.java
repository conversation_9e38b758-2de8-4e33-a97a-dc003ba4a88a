package com.mazai.pos.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;

import com.mazai.pos.model.User;
import com.mazai.pos.repository.UserRepository;

// @Component - Disabled to prevent automatic user creation
public class DataInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        logger.info("Checking if admin user exists...");

        try {
            // Initialize admin user if not exists
            if (!userRepository.existsByUsername("admin")) {
                logger.info("Admin user does not exist. Creating admin user...");

                User adminUser = new User();
                adminUser.setUsername("admin");
                // 使用密码编码器加密密码
                String encodedPassword = passwordEncoder.encode("admin123");
                adminUser.setPassword(encodedPassword);
                adminUser.setEmail("<EMAIL>");
                adminUser.setRole("ADMIN");
                adminUser.setActive(true);

                User savedUser = userRepository.save(adminUser);
                logger.info("Admin user created successfully with ID: {}", savedUser.getId());
            } else {
                logger.info("Admin user already exists, skipping creation");
            }

            // Initialize test user if not exists
            if (!userRepository.existsByUsername("user")) {
                logger.info("Test user does not exist. Creating test user...");

                User testUser = new User();
                testUser.setUsername("user");
                // 使用密码编码器加密密码
                String encodedPassword = passwordEncoder.encode("user123");
                testUser.setPassword(encodedPassword);
                testUser.setEmail("<EMAIL>");
                testUser.setRole("USER");
                testUser.setActive(true);

                User savedTestUser = userRepository.save(testUser);
                logger.info("Test user created successfully with ID: {}", savedTestUser.getId());
            } else {
                logger.info("Test user already exists, skipping creation");
            }

            // 验证用户数量
            long userCount = userRepository.count();
            logger.info("Total users in database: {}", userCount);
        } catch (Exception e) {
            logger.error("Error initializing admin user: {}", e.getMessage(), e);
            throw e;
        }
    }
}

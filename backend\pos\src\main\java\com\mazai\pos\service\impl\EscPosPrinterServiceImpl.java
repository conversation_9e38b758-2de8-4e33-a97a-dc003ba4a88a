package com.mazai.pos.service.impl;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.Socket;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mazai.pos.config.PrinterConfig;
import com.mazai.pos.model.Order;
import com.mazai.pos.model.OrderItem;
import com.mazai.pos.model.Payment;
import com.mazai.pos.model.Product;
import com.mazai.pos.model.ProductVariant;
import com.mazai.pos.service.PaymentService;
import com.mazai.pos.service.PrinterService;
import com.mazai.pos.service.ProductVariantService;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

/**
 * ESC/POS网络打印服务实现类
 * 通过TCP Socket连接网络打印机，发送ESC/POS命令
 */
@Service
public class EscPosPrinterServiceImpl implements PrinterService {

    private static final Logger logger = LoggerFactory.getLogger(EscPosPrinterServiceImpl.class);

    @Autowired
    private PrinterConfig printerConfig;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private ProductVariantService productVariantService;

    private boolean connected = false;

    // ESC/POS 命令常量
    private static final byte[] INIT = {0x1B, 0x40}; // 初始化打印机
    private static final byte[] CUT = {0x1D, 0x56, 0x00}; // 全切
    private static final byte[] ALIGN_LEFT = {0x1B, 0x61, 0x00}; // 左对齐
    private static final byte[] ALIGN_CENTER = {0x1B, 0x61, 0x01}; // 居中对齐
    private static final byte[] BOLD_ON = {0x1B, 0x45, 0x01}; // 粗体开
    private static final byte[] BOLD_OFF = {0x1B, 0x45, 0x00}; // 粗体关
    private static final byte[] LF = {0x0A}; // 换行
    private static final byte[] FEED_LINES = {0x1B, 0x64, 0x03}; // 进纸3行
    private static final byte[] LARGE_FONT = {0x1D, 0x21, 0x11}; // 双倍高度和宽度
    private static final byte[] MEDIUM_FONT = {0x1D, 0x21, 0x01}; // 双倍高度
    private static final byte[] NORMAL_FONT = {0x1D, 0x21, 0x00}; // 正常字体


    @PostConstruct
    public void init() {
        if (printerConfig.isEnabled() && "ESCPOS".equals(printerConfig.getType())) {
            logger.info("初始化ESC/POS网络打印机连接");
            connect();
        } else {
            logger.info("ESC/POS打印机功能未启用或类型不匹配，跳过初始化连接");
        }
    }

    @Override
    public boolean connect() {
        if (!printerConfig.isEnabled() || !"ESCPOS".equals(printerConfig.getType())) {
            logger.info("打印机功能未启用或类型不匹配，跳过连接");
            return false;
        }

        try {
            // 测试连接
            if (testConnection()) {
                connected = true;
                logger.info("ESC/POS网络打印机连接成功: {}:{}",
                    printerConfig.getNetwork().getIp(),
                    printerConfig.getNetwork().getPort());
                return true;
            } else {
                connected = false;
                logger.error("ESC/POS网络打印机连接失败");
                return false;
            }
        } catch (Exception e) {
            connected = false;
            logger.error("连接ESC/POS网络打印机时出错: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void disconnect() {
        connected = false;
        logger.info("已断开与ESC/POS网络打印机的连接");
    }

    @Override
    public boolean isConnected() {
        return connected;
    }

    @Override
    public boolean printOrder(Order order) {
        return printOrderWithReceiptType(order, "CHINESE");
    }

    /**
     * 打印订单收据（支持多语言）
     * @param order 要打印的订单
     * @param receiptType 收据类型 (CHINESE/MALAY)
     * @return 是否打印成功
     */
    public boolean printOrderWithReceiptType(Order order, String receiptType) {
        if (!printerConfig.isEnabled() || !"ESCPOS".equals(printerConfig.getType())) {
            logger.info("打印机功能未启用或类型不匹配，跳过打印");
            return false;
        }

        try {
            logger.info("开始打印订单 #{} (收据类型: {})", order.getId(), receiptType);

            List<byte[]> commands = new ArrayList<>();

            // 初始化打印机
            commands.add(INIT);

            // 店铺信息（居中）
            commands.add(ALIGN_CENTER);
            commands.add("KEDAI MAKANAN DAN MINUMAN MAZAI\n".getBytes(getCharset()));
            commands.add("No.121 Kampung Baru Mahsan , Bahau\n".getBytes(getCharset()));
            commands.add("Tel:06-4545105\n".getBytes(getCharset()));

            // 分隔线
            commands.add(ALIGN_LEFT);
            commands.add(generateSeparatorLine('-', 48));
            commands.add(LF);

            // Receipt Number - 使用更合适的标签
            commands.add(String.format("Receipt No: %d\n", order.getId()).getBytes(getCharset()));

            // 日期时间 - 统一使用英文，使用updatedAt
            if (order.getUpdatedAt() != null) {
                commands.add(String.format("Date: %s\n",
                    order.getUpdatedAt().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .getBytes(getCharset()));
            } else {
                // 如果updatedAt为空，使用当前时间
                commands.add(String.format("Date: %s\n",
                    java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .getBytes(getCharset()));
            }

            // 分隔线
            commands.add(generateSeparatorLine('-', 48));
            commands.add(LF);

            // 商品列表标题 - 统一使用英文
            commands.add(String.format("%-25s %5s %10s\n", "Item", "Qty", "Total").getBytes(getCharset()));
            commands.add(generateSeparatorLine('-', 48));
            commands.add(LF);

            // 商品列表 - 使用固定宽度确保完美对齐，支持变体显示
            for (OrderItem item : order.getOrderItems()) {
                // 获取带变体的商品名称
                String productName = getProductNameWithVariants(item, receiptType);

                // 确保商品名称不超过25个字符，并使用固定宽度
                productName = truncateString(productName, 25);
                commands.add(String.format("%-25s %5d %10.2f\n",
                    productName,
                    item.getQuantity(),
                    item.getSubtotal()).getBytes(getCharset()));
            }

            // 分隔线
            commands.add(generateSeparatorLine('-', 48));
            commands.add(LF);

            // 总计 - 统一使用英文
            commands.add(String.format("%-30s %10.2f\n", "Total:", order.getTotalAmount()).getBytes(getCharset()));
            commands.add(LF);

            // 支付信息 - 从数据库获取真实支付数据
            List<Payment> payments = paymentService.getPaymentsByOrder(order);
            if (!payments.isEmpty()) {
                Payment latestPayment = payments.get(payments.size() - 1); // 获取最新的支付记录

                // 支付方式显示 - 统一使用英文
                String paymentMethodDisplay = "CASH".equals(latestPayment.getPaymentMethod()) ? "CASH" : latestPayment.getPaymentMethod();
                commands.add(String.format("Payment Method: %s\n", paymentMethodDisplay).getBytes(getCharset()));

                // 实际支付金额 - 统一使用英文
                BigDecimal paidAmount = latestPayment.getPaid() != null ? latestPayment.getPaid() : latestPayment.getAmount();
                commands.add(String.format("%-30s %10.2f\n", "Paid:", paidAmount).getBytes(getCharset()));

                // 找零 - 统一使用英文
                BigDecimal changeAmount = latestPayment.getChangeAmount() != null ? latestPayment.getChangeAmount() : BigDecimal.ZERO;
                commands.add(String.format("%-30s %10.2f\n", "Change:", changeAmount).getBytes(getCharset()));
            } else {
                // 如果没有支付记录，使用默认值 - 统一使用英文
                commands.add("Payment Method: \n".getBytes(getCharset()));
                commands.add(String.format("%-30s %10.2f\n", "Paid:", order.getTotalAmount()).getBytes(getCharset()));
                commands.add(String.format("%-30s %10.2f\n", "Change:", 0.00).getBytes(getCharset()));
            }
            commands.add(LF);

            // 结尾信息 - 统一使用英文
            commands.add(ALIGN_CENTER);
            commands.add("Thank you!\n".getBytes(getCharset()));
            commands.add("Please come again!\n".getBytes(getCharset()));
            commands.add(LF);

            // 进纸并切纸
            commands.add(FEED_LINES);
            commands.add(CUT);

            // 发送到打印机
            return sendToPrinter(commands);

        } catch (Exception e) {
            logger.error("打印订单时出错: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getPrinterName() {
        return printerConfig.getName();
    }

    @Override
    public boolean printTestPage() {
        if (!printerConfig.isEnabled() || !"ESCPOS".equals(printerConfig.getType())) {
            logger.info("打印机功能未启用或类型不匹配，跳过打印测试页");
            return false;
        }

        try {
            logger.info("开始打印测试页");

            List<byte[]> commands = new ArrayList<>();

            // 初始化打印机
            commands.add(INIT);

            // 测试页标题
            commands.add(ALIGN_CENTER);
            commands.add(BOLD_ON);
            commands.add("ESC/POS 打印机测试\n".getBytes(getCharset()));
            commands.add("PRINTER TEST\n".getBytes(getCharset()));
            commands.add(BOLD_OFF);
            commands.add(LF);

            // 测试信息
            commands.add(ALIGN_LEFT);
            commands.add(generateSeparatorLine('-', 48));
            commands.add(LF);
            commands.add(String.format("日期: %s\n",
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).getBytes(getCharset()));
            commands.add(String.format("打印机: %s\n", printerConfig.getName()).getBytes(getCharset()));
            commands.add(String.format("IP地址: %s:%d\n",
                printerConfig.getNetwork().getIp(),
                printerConfig.getNetwork().getPort()).getBytes(getCharset()));
            commands.add(String.format("编码: %s\n", printerConfig.getEncoding()).getBytes(getCharset()));
            commands.add(generateSeparatorLine('-', 48));
            commands.add(LF);

            // 中文测试
            commands.add("中文测试: 这是中文字符测试\n".getBytes(getCharset()));
            commands.add("English Test: This is English text\n".getBytes(getCharset()));
            commands.add(LF);

            // 结尾
            commands.add(ALIGN_CENTER);
            commands.add("测试完成\n".getBytes(getCharset()));
            commands.add("Test Complete\n".getBytes(getCharset()));
            commands.add(LF);

            // 进纸并切纸
            commands.add(FEED_LINES);
            commands.add(CUT);

            // 发送到打印机
            return sendToPrinter(commands);

        } catch (Exception e) {
            logger.error("打印测试页时出错: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean printSimpleTest(String content) {
        if (!printerConfig.isEnabled() || !"ESCPOS".equals(printerConfig.getType())) {
            logger.info("打印机功能未启用或类型不匹配，跳过打印简单测试");
            return false;
        }

        try {
            logger.info("开始打印简单测试，内容: {}", content);

            List<byte[]> commands = new ArrayList<>();

            // 初始化打印机
            commands.add(INIT);

            // 内容
            commands.add(ALIGN_CENTER);
            commands.add(BOLD_ON);
            commands.add("简单测试\n".getBytes(getCharset()));
            commands.add(BOLD_OFF);
            commands.add(LF);

            commands.add(ALIGN_LEFT);
            if (content != null && !content.trim().isEmpty()) {
                commands.add((content + "\n").getBytes(getCharset()));
            } else {
                commands.add("这是一个简单的打印测试\n".getBytes(getCharset()));
            }
            commands.add(LF);

            commands.add(String.format("时间: %s\n",
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).getBytes(getCharset()));
            commands.add(LF);

            // 进纸并切纸
            commands.add(FEED_LINES);
            commands.add(CUT);

            // 发送到打印机
            return sendToPrinter(commands);

        } catch (Exception e) {
            logger.error("打印简单测试时出错: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean printReceiptTest() {
        // 创建一个测试订单来打印收据测试
        Order testOrder = createTestOrder();
        return printOrder(testOrder);
    }

    /**
     * 使用指定打印机打印订单
     * @param order 要打印的订单
     * @param printerName 打印机名称
     * @return 是否打印成功
     */
    public boolean printOrderWithPrinter(Order order, String printerName) {
        return printOrderWithPrinterAndReceiptType(order, printerName, "CHINESE");
    }

    /**
     * 使用指定打印机打印订单（支持多语言）
     * @param order 要打印的订单
     * @param printerName 打印机名称
     * @param receiptType 收据类型 (CHINESE/MALAY)
     * @return 是否打印成功
     */
    public boolean printOrderWithPrinterAndReceiptType(Order order, String printerName, String receiptType) {
        if (!printerConfig.isEnabled() || !"ESCPOS".equals(printerConfig.getType())) {
            logger.info("打印机功能未启用或类型不匹配，跳过打印");
            return false;
        }

        PrinterConfig.Network networkConfig = printerConfig.getPrinterConfig(printerName);
        if (networkConfig == null) {
            logger.error("找不到打印机配置: {}", printerName);
            return false;
        }

        try {
            logger.info("开始使用{}打印订单 #{} (收据类型: {})", printerName, order.getId(), receiptType);

            List<byte[]> commands = new ArrayList<>();

            // 初始化打印机
            commands.add(INIT);

            // 店铺信息（居中）
            commands.add(ALIGN_CENTER);
            commands.add("KEDAI MAKANAN DAN MINUMAN MAZAI\n".getBytes(getCharset()));
            commands.add("No.121 Kampung Baru Mahsan , Bahau\n".getBytes(getCharset()));
            commands.add("Tel:06-4545105\n".getBytes(getCharset()));

            // 分隔线
            commands.add(ALIGN_LEFT);
            commands.add(generateSeparatorLine('-', 48));
            commands.add(LF);

            // Receipt Number - 使用更合适的标签
            commands.add(String.format("Receipt No: %d\n", order.getId()).getBytes(getCharset()));

            // 日期时间 - 统一使用英文，使用updatedAt
            commands.add(String.format("Date: %s\n",
                order.getUpdatedAt().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .getBytes(getCharset()));

            // 分隔线
            commands.add(generateSeparatorLine('-', 48));
            commands.add(LF);

            // 商品列表标题 - 统一使用英文，与新的对齐方式匹配
            String headerLine = formatReceiptHeaderLine();
            commands.add(headerLine.getBytes(getCharset()));
            commands.add(generateSeparatorLine('-', 48));
            commands.add(LF);

            // 商品列表 - 使用固定位置对齐，支持变体显示
            for (OrderItem item : order.getOrderItems()) {
                // 获取带变体的商品名称
                String productName = getProductNameWithVariants(item, receiptType);

                // 使用固定位置对齐方法
                String itemLine = formatReceiptItemLine(productName, item.getQuantity(), item.getSubtotal());
                commands.add(itemLine.getBytes(getCharset()));
            }

            // 分隔线
            commands.add(generateSeparatorLine('-', 48));
            commands.add(LF);

            // 总计 - 统一使用英文
            commands.add(String.format("%-30s %10.2f\n", "Total:", order.getTotalAmount()).getBytes(getCharset()));
            commands.add(LF);

            // 支付信息 - 从数据库获取真实支付数据
            List<Payment> payments = paymentService.getPaymentsByOrder(order);
            if (!payments.isEmpty()) {
                Payment latestPayment = payments.get(payments.size() - 1); // 获取最新的支付记录

                // 支付方式显示 - 统一使用英文
                String paymentMethodDisplay = "CASH".equals(latestPayment.getPaymentMethod()) ? "CASH" : latestPayment.getPaymentMethod();
                commands.add(String.format("Payment Method: %s\n", paymentMethodDisplay).getBytes(getCharset()));

                // 实际支付金额 - 统一使用英文
                BigDecimal paidAmount = latestPayment.getPaid() != null ? latestPayment.getPaid() : latestPayment.getAmount();
                commands.add(String.format("%-30s %10.2f\n", "Paid:", paidAmount).getBytes(getCharset()));

                // 找零 - 统一使用英文
                BigDecimal changeAmount = latestPayment.getChangeAmount() != null ? latestPayment.getChangeAmount() : BigDecimal.ZERO;
                commands.add(String.format("%-30s %10.2f\n", "Change:", changeAmount).getBytes(getCharset()));
            } else {
                // 如果没有支付记录，使用默认值 - 统一使用英文
                commands.add("Payment Method: \n".getBytes(getCharset()));
                commands.add(String.format("%-30s %10.2f\n", "Paid:", order.getTotalAmount()).getBytes(getCharset()));
                commands.add(String.format("%-30s %10.2f\n", "Change:", 0.00).getBytes(getCharset()));
            }
            commands.add(LF);

            // 结尾信息 - 统一使用英文
            commands.add(ALIGN_CENTER);
            commands.add("Thank you!\n".getBytes(getCharset()));
            commands.add("Please come again!\n".getBytes(getCharset()));
            commands.add(LF);

            // 进纸并切纸
            commands.add(FEED_LINES);
            commands.add(CUT);

            // 发送到指定打印机
            return sendToPrinterWithConfig(commands, networkConfig);

        } catch (Exception e) {
            logger.error("使用{}打印订单时出错: {}", printerName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 打印厨房订单（不带价格，突出商品和备注）
     * @param order 要打印的订单
     * @return 是否打印成功
     */
    public boolean printKitchenOrder(Order order) {
        return printKitchenOrderWithType(order, "NEW");
    }

    /**
     * 打印厨房订单（支持订单类型标识）
     * @param order 要打印的订单
     * @param orderType 订单类型 (NEW/UPDATE)
     * @return 是否打印成功
     */
    public boolean printKitchenOrderWithType(Order order, String orderType) {
        if (!printerConfig.isEnabled() || !"ESCPOS".equals(printerConfig.getType())) {
            logger.info("打印机功能未启用或类型不匹配，跳过厨房订单打印");
            return false;
        }

        try {
            logger.info("开始打印厨房订单 #{} (类型: {})", order.getId(), orderType);

            List<byte[]> commands = new ArrayList<>();

            // 初始化打印机
            commands.add(INIT);

            // 订单类型标题（居中，粗体，大字体）
            commands.add(ALIGN_CENTER);
            commands.add(BOLD_ON);
            commands.add(LARGE_FONT); // 使用大字体
            if ("UPDATE".equals(orderType)) {
                commands.add("UPDATE ORDER\n".getBytes(getCharset()));
            } else {
                commands.add("NEW ORDER\n".getBytes(getCharset()));
            }
            commands.add(NORMAL_FONT); // 恢复普通字体
            commands.add(BOLD_OFF);

            // 厨房订单副标题
            commands.add(BOLD_ON);
            commands.add(MEDIUM_FONT); // 使用中等字体
            commands.add("KITCHEN ORDER\n".getBytes(getCharset()));
            commands.add(NORMAL_FONT); // 恢复普通字体
            commands.add(BOLD_OFF);

            // 更长的分隔线开始
            commands.add(ALIGN_LEFT);
            commands.add(generateSeparatorLine('=', 48));
            commands.add(LF);

            // 订单信息
            commands.add(MEDIUM_FONT);
            commands.add(String.format("订单号：%d\n", order.getId()).getBytes(getCharset()));
            commands.add(String.format("时间：%s\n",
                order.getUpdatedAt().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm a")))
                .getBytes(getCharset()));
            if (order.getTableNumber() != null && !order.getTableNumber().trim().isEmpty()) {
                commands.add(String.format("桌号：%s\n", order.getTableNumber()).getBytes(getCharset()));
            }

            // 更长的分隔线
            commands.add(NORMAL_FONT); // 恢复普通字体
            commands.add(generateSeparatorLine('-', 48));
            commands.add(LF);
            commands.add(LARGE_FONT); // 恢复普通字体


            // 商品列表（按您要求的格式：数量x 商品名 (备注)）
            for (OrderItem item : order.getOrderItems()) {
                String itemLine = String.format("%dx %s",
                    item.getQuantity(),
                    item.getProduct().getName());

                // 添加备注（如果有）
                if (item.getRemark() != null && !item.getRemark().trim().isEmpty()) {
                    itemLine += String.format("  (%s)", item.getRemark());
                }

                commands.add((itemLine + "\n").getBytes(getCharset()));
            }
            commands.add(NORMAL_FONT); // 恢复普通字体

            // 更长的分隔线结束
            commands.add(generateSeparatorLine('=', 48));
            commands.add(LF);

            // 进纸并切纸
            commands.add(FEED_LINES);
            commands.add(CUT);

            // 发送到打印机
            return sendToPrinter(commands);

        } catch (Exception e) {
            logger.error("打印厨房订单时出错: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 使用指定打印机打印厨房订单（不带价格，突出商品和备注）
     * @param order 要打印的订单
     * @param printerName 打印机名称
     * @return 是否打印成功
     */
    public boolean printKitchenOrderWithPrinter(Order order, String printerName) {
        return printKitchenOrderWithPrinterAndType(order, printerName, "NEW");
    }

    /**
     * 使用指定打印机打印厨房订单（支持订单类型标识）
     * @param order 要打印的订单
     * @param printerName 打印机名称
     * @param orderType 订单类型 (NEW/UPDATE)
     * @return 是否打印成功
     */
    public boolean printKitchenOrderWithPrinterAndType(Order order, String printerName, String orderType) {
        if (!printerConfig.isEnabled() || !"ESCPOS".equals(printerConfig.getType())) {
            logger.info("打印机功能未启用或类型不匹配，跳过厨房订单打印");
            return false;
        }

        PrinterConfig.Network networkConfig = printerConfig.getPrinterConfig(printerName);
        if (networkConfig == null) {
            logger.error("找不到打印机配置: {}", printerName);
            return false;
        }

        try {
            logger.info("开始打印厨房订单 #{} (类型: {})", order.getId(), orderType);

            List<byte[]> commands = new ArrayList<>();

            // 初始化打印机
            commands.add(INIT);

            // 订单类型标题（居中，粗体，大字体）
            commands.add(ALIGN_CENTER);
            commands.add(BOLD_ON);
            commands.add(LARGE_FONT); // 使用大字体
            if ("UPDATE".equals(orderType)) {
                commands.add("UPDATE ORDER\n".getBytes(getCharset()));
            } else {
                commands.add("NEW ORDER\n".getBytes(getCharset()));
            }
            commands.add(NORMAL_FONT); // 恢复普通字体
            commands.add(BOLD_OFF);

            // 厨房订单副标题
            commands.add(BOLD_ON);
            commands.add(MEDIUM_FONT); // 使用中等字体
            commands.add("KITCHEN ORDER\n".getBytes(getCharset()));
            commands.add(NORMAL_FONT); // 恢复普通字体
            commands.add(BOLD_OFF);

            // 更长的分隔线开始
            commands.add(ALIGN_LEFT);
            commands.add(generateSeparatorLine('=', 48));
            commands.add(LF);

            // 订单信息
            commands.add(MEDIUM_FONT);
            commands.add(String.format("订单号：%d\n", order.getId()).getBytes(getCharset()));
            commands.add(String.format("时间：%s\n",
                order.getUpdatedAt().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm a")))
                .getBytes(getCharset()));
            if (order.getTableNumber() != null && !order.getTableNumber().trim().isEmpty()) {
                commands.add(String.format("桌号：%s\n", order.getTableNumber()).getBytes(getCharset()));
            }

            // 更长的分隔线
            commands.add(NORMAL_FONT); // 恢复普通字体
            commands.add(generateSeparatorLine('-', 48));
            commands.add(LF);
            commands.add(LARGE_FONT); // 恢复普通字体


            // 商品列表（按您要求的格式：数量x 商品名 (备注)）
            for (OrderItem item : order.getOrderItems()) {
                String itemLine = String.format("%dx %s",
                    item.getQuantity(),
                    item.getProduct().getName());

                // 添加备注（如果有）
                if (item.getRemark() != null && !item.getRemark().trim().isEmpty()) {
                    itemLine += String.format("  (%s)", item.getRemark());
                }

                commands.add((itemLine + "\n").getBytes(getCharset()));
            }
            commands.add(NORMAL_FONT); // 恢复普通字体

            // 更长的分隔线结束
            commands.add(generateSeparatorLine('=', 48));
            commands.add(LF);

            // 进纸并切纸
            commands.add(FEED_LINES);
            commands.add(CUT);

            // 发送到指定打印机
            return sendToPrinterWithConfig(commands, networkConfig);

        } catch (Exception e) {
            logger.error("使用{}打印厨房订单时出错: {}", printerName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 测试打印机连接
     */
    private boolean testConnection() {
        try (Socket socket = new Socket()) {
            socket.connect(new java.net.InetSocketAddress(
                printerConfig.getNetwork().getIp(),
                printerConfig.getNetwork().getPort()),
                printerConfig.getNetwork().getTimeout());
            return true;
        } catch (IOException e) {
            logger.error("测试连接失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 发送命令到打印机
     */
    private boolean sendToPrinter(List<byte[]> commands) {
        return sendToPrinterWithConfig(commands, printerConfig.getNetwork());
    }

    /**
     * 发送命令到指定的打印机
     */
    private boolean sendToPrinterWithConfig(List<byte[]> commands, PrinterConfig.Network networkConfig) {
        try (Socket socket = new Socket()) {
            socket.connect(new java.net.InetSocketAddress(
                networkConfig.getIp(),
                networkConfig.getPort()),
                networkConfig.getTimeout());

            try (OutputStream out = socket.getOutputStream()) {
                for (byte[] command : commands) {
                    out.write(command);
                }
                out.flush();
            }

            logger.info("成功发送打印命令到打印机 {}:{}", networkConfig.getIp(), networkConfig.getPort());
            return true;

        } catch (IOException e) {
            logger.error("发送打印命令失败 {}:{} - {}", networkConfig.getIp(), networkConfig.getPort(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 生成分隔线
     */
    private byte[] generateSeparatorLine(char ch, int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(ch);
        }
        sb.append('\n');
        return sb.toString().getBytes(getCharset());
    }

    /**
     * 截断字符串到指定长度
     */
    private String truncateString(String str, int maxLength) {
        if (str == null) return "";
        if (str.length() <= maxLength) return str;
        return str.substring(0, maxLength - 3) + "...";
    }

    /**
     * 获取带变体的商品名称
     * @param item 订单项
     * @param receiptType 收据类型 (CHINESE/MALAY)
     * @return 格式化的商品名称
     */
    private String getProductNameWithVariants(OrderItem item, String receiptType) {
        String productName;

        // 根据收据类型选择商品名称
        if ("MALAY".equals(receiptType)) {
            // 国文收据使用 altname，如果没有则使用 name
            productName = item.getProduct().getAltname() != null && !item.getProduct().getAltname().trim().isEmpty()
                ? item.getProduct().getAltname()
                : item.getProduct().getName();
        } else {
            // 华语收据使用 name
            productName = item.getProduct().getName();
        }

        // 处理变体
        if (item.getVariantIds() != null && !item.getVariantIds().trim().isEmpty()) {
            try {
                // 解析变体ID JSON字符串
                String variantIdsJson = item.getVariantIds().trim();
                if (variantIdsJson.startsWith("[") && variantIdsJson.endsWith("]")) {
                    // 移除方括号并分割ID
                    String idsString = variantIdsJson.substring(1, variantIdsJson.length() - 1);
                    if (!idsString.trim().isEmpty()) {
                        String[] idStrings = idsString.split(",");
                        List<Long> variantIds = new ArrayList<>();

                        for (String idStr : idStrings) {
                            try {
                                variantIds.add(Long.parseLong(idStr.trim()));
                            } catch (NumberFormatException e) {
                                // 忽略无效的ID
                            }
                        }

                        if (!variantIds.isEmpty()) {
                            // 获取变体信息
                            List<ProductVariant> variants = productVariantService.getVariantsByIds(variantIds);

                            // 查找 "NOODLE" 类型的变体
                            for (ProductVariant variant : variants) {
                                if ("NOODLE".equals(variant.getVariantType())) {
                                    String variantName = getNoodleVariantName(variant.getName(), receiptType);

                                    if (variantName != null && !variantName.trim().isEmpty()) {
                                        productName = productName + variantName;
                                    }
                                    break; // 只处理第一个 NOODLE 类型的变体
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                logger.warn("解析变体ID时出错: {}", e.getMessage());
            }
        }

        return productName;
    }

    /**
     * 获取 NOODLE 类型变体的名称
     * @param variantName 完整的变体名称 (格式: "米粉,mihun")
     * @param receiptType 收据类型 (CHINESE/MALAY)
     * @return 格式化的变体名称
     */
    private String getNoodleVariantName(String variantName, String receiptType) {
        if (variantName == null || variantName.trim().isEmpty()) {
            return "";
        }

        if ("MALAY".equals(receiptType)) {
            // 马来文收据：使用 "," 后面的部分 (国语名称)
            if (variantName.contains(",")) {
                String[] parts = variantName.split(",", 2);
                if (parts.length > 1) {
                    return parts[1].trim();
                }
            }
        } else {
            // 中文收据：使用 "," 前面的部分 (华语名称)
            if (variantName.contains(",")) {
                String[] parts = variantName.split(",", 2);
                if (parts.length > 0) {
                    return parts[0].trim();
                }
            }
        }

        // 如果没有找到分隔符，返回原始名称
        return variantName;
    }

    /**
     * 格式化收据表头行
     * @return 格式化的表头字符串
     */
    private String formatReceiptHeaderLine() {
        // 设置固定位置（与项目行保持一致）
        final int PRODUCT_NAME_WIDTH = 30;  // 产品名称区域宽度
        final int QUANTITY_WIDTH = 5;       // 数量区域宽度
        final int TOTAL_WIDTH = 10;         // 总价区域宽度

        // 格式化表头
        String itemHeader = padStringToWidth("Item", PRODUCT_NAME_WIDTH);
        String qtyHeader = String.format("%" + QUANTITY_WIDTH + "s", "Qty");
        String totalHeader = String.format("%" + TOTAL_WIDTH + "s", "Total");

        return itemHeader + qtyHeader + totalHeader + "\n";
    }

    /**
     * 格式化收据项目行，确保固定位置对齐
     * @param productName 产品名称
     * @param quantity 数量
     * @param subtotal 小计
     * @return 格式化的行字符串
     */
    private String formatReceiptItemLine(String productName, Integer quantity, BigDecimal subtotal) {
        // 设置固定位置
        final int PRODUCT_NAME_WIDTH = 30;  // 产品名称区域宽度
        final int QUANTITY_WIDTH = 5;       // 数量区域宽度
        final int TOTAL_WIDTH = 10;         // 总价区域宽度

        // 处理产品名称，确保不超过指定宽度
        String formattedProductName = padStringToWidth(productName, PRODUCT_NAME_WIDTH);

        // 格式化数量（右对齐）
        String formattedQuantity = String.format("%" + QUANTITY_WIDTH + "d", quantity);

        // 格式化总价（右对齐，保留2位小数）
        String formattedTotal = String.format("%" + TOTAL_WIDTH + ".2f", subtotal);

        // 组合成一行
        return formattedProductName + formattedQuantity + formattedTotal + "\n";
    }

    /**
     * 将字符串填充或截断到指定宽度，考虑中文字符的显示宽度
     * @param str 原字符串
     * @param targetWidth 目标宽度
     * @return 格式化后的字符串
     */
    private String padStringToWidth(String str, int targetWidth) {
        if (str == null) str = "";

        // 计算字符串的显示宽度（中文字符算2个宽度，英文字符算1个宽度）
        int displayWidth = calculateDisplayWidth(str);

        if (displayWidth > targetWidth) {
            // 如果超过目标宽度，需要截断
            return truncateToDisplayWidth(str, targetWidth - 3) + "...";
        } else {
            // 如果小于目标宽度，需要填充空格
            int spacesToAdd = targetWidth - displayWidth;
            return str + " ".repeat(spacesToAdd);
        }
    }

    /**
     * 计算字符串的显示宽度（中文字符算2个宽度，英文字符算1个宽度）
     * @param str 字符串
     * @return 显示宽度
     */
    private int calculateDisplayWidth(String str) {
        if (str == null) return 0;

        int width = 0;
        for (char c : str.toCharArray()) {
            if (isChinese(c)) {
                width += 2; // 中文字符占2个宽度
            } else {
                width += 1; // 英文字符占1个宽度
            }
        }
        return width;
    }

    /**
     * 截断字符串到指定的显示宽度
     * @param str 原字符串
     * @param maxDisplayWidth 最大显示宽度
     * @return 截断后的字符串
     */
    private String truncateToDisplayWidth(String str, int maxDisplayWidth) {
        if (str == null) return "";

        StringBuilder result = new StringBuilder();
        int currentWidth = 0;

        for (char c : str.toCharArray()) {
            int charWidth = isChinese(c) ? 2 : 1;
            if (currentWidth + charWidth > maxDisplayWidth) {
                break;
            }
            result.append(c);
            currentWidth += charWidth;
        }

        return result.toString();
    }

    /**
     * 判断字符是否为中文字符
     * @param c 字符
     * @return 是否为中文字符
     */
    private boolean isChinese(char c) {
        return c >= 0x4E00 && c <= 0x9FFF; // 基本中文字符范围
    }

    /**
     * 获取字符编码
     */
    private Charset getCharset() {
        try {
            return Charset.forName(printerConfig.getEncoding());
        } catch (Exception e) {
            logger.warn("不支持的编码 {}，使用默认编码", printerConfig.getEncoding());
            return Charset.defaultCharset();
        }
    }

    /**
     * 创建测试订单
     */
    private Order createTestOrder() {
        Order order = new Order();
        order.setId(999L);
        order.setTableNumber("5");
        // orderDate已删除，使用createdAt和updatedAt
        order.setTotalAmount(new BigDecimal("25.00"));

        // 创建测试商品和订单项
        List<OrderItem> orderItems = new ArrayList<>();

        // 测试商品1
        OrderItem item1 = new OrderItem();
        item1.setQuantity(1);
        item1.setSubtotal(new BigDecimal("12.00"));
        // 创建一个简单的测试产品对象
        Product product1 = new Product();
        product1.setName("宫保鸡丁");
        item1.setProduct(product1);
        item1.setRemark("少辣");
        orderItems.add(item1);

        // 测试商品2
        OrderItem item2 = new OrderItem();
        item2.setQuantity(2);
        item2.setSubtotal(new BigDecimal("10.00"));
        Product product2 = new Product();
        product2.setName("鱼香肉丝");
        item2.setProduct(product2);
        item2.setRemark("不要胡萝卜");
        orderItems.add(item2);

        // 测试商品3
        OrderItem item3 = new OrderItem();
        item3.setQuantity(3);
        item3.setSubtotal(new BigDecimal("3.00"));
        Product product3 = new Product();
        product3.setName("米饭");
        item3.setProduct(product3);
        orderItems.add(item3);

        order.setOrderItems(orderItems);
        return order;
    }

    @PreDestroy
    public void destroy() {
        logger.info("销毁ESC/POS网络打印机服务");
        disconnect();
    }
}

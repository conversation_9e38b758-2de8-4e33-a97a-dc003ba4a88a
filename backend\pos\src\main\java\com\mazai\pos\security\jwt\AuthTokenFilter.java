package com.mazai.pos.security.jwt;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public class AuthTokenFilter extends OncePerRequestFilter {
    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private UserDetailsService userDetailsService;

    private static final Logger logger = LoggerFactory.getLogger(AuthTokenFilter.class);

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        try {
            String requestURI = request.getRequestURI();
            String method = request.getMethod();
            logger.debug("Processing request: {} {}", method, requestURI);

            // 跳过对/mobile/**路径的处理
            if (requestURI.startsWith("/mobile/")) {
                logger.debug("Skipping JWT authentication for mobile API: {}", requestURI);
                filterChain.doFilter(request, response);
                return;
            }

            // 记录所有请求头，帮助调试
            logger.debug("Request headers:");
            java.util.Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                logger.debug("  {}: {}", headerName, request.getHeader(headerName));
            }

            String jwt = parseJwt(request);
            if (jwt != null) {
                logger.debug("JWT token found, length: {}", jwt.length());
                logger.debug("JWT token first 20 chars: {}", jwt.substring(0, Math.min(20, jwt.length())) + "...");

                boolean isValid = jwtUtils.validateJwtToken(jwt);
                logger.debug("JWT token valid: {}", isValid);

                if (isValid) {
                    String username = jwtUtils.getUserNameFromJwtToken(jwt);
                    logger.debug("Username from JWT: {}", username);

                    try {
                        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                        logger.debug("User details loaded for username: {}", username);
                        logger.debug("User authorities: {}", userDetails.getAuthorities());

                        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                userDetails, null, userDetails.getAuthorities());
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                        SecurityContextHolder.getContext().setAuthentication(authentication);
                        logger.debug("Authentication set in SecurityContext for user: {}", username);
                    } catch (Exception e) {
                        logger.error("Error loading user details for username {}: {}", username, e.getMessage(), e);
                    }
                } else {
                    logger.debug("JWT token is invalid, not setting authentication");
                    // 记录更多关于无效令牌的信息
                    try {
                        String[] parts = jwt.split("\\.");
                        if (parts.length == 3) {
                            logger.debug("JWT structure appears valid (has 3 parts)");
                            // 解析JWT头部和载荷（不解码签名）
                            String header = new String(java.util.Base64.getDecoder().decode(parts[0]));
                            String payload = new String(java.util.Base64.getDecoder().decode(parts[1]));
                            logger.debug("JWT header: {}", header);
                            logger.debug("JWT payload: {}", payload);
                        } else {
                            logger.debug("JWT structure is invalid (does not have 3 parts)");
                        }
                    } catch (Exception e) {
                        logger.debug("Error parsing JWT token: {}", e.getMessage());
                    }


                }
            } else {
                logger.debug("No JWT token found in request, not setting authentication");
            }
        } catch (Exception e) {
            logger.error("Cannot set user authentication: {}", e.getMessage(), e);
        }

        filterChain.doFilter(request, response);
    }

    private String parseJwt(HttpServletRequest request) {
        String headerAuth = request.getHeader("Authorization");
        logger.debug("Authorization header: {}", headerAuth);

        // 记录请求方法和 URL
        logger.debug("Request method: {}", request.getMethod());
        logger.debug("Request URL: {}", request.getRequestURL());

        // 记录所有请求头
        java.util.Enumeration<String> headerNames = request.getHeaderNames();
        if (headerNames != null) {
            logger.debug("All request headers:");
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                logger.debug("  {}: {}", headerName, request.getHeader(headerName));
            }
        }

        if (StringUtils.hasText(headerAuth) && headerAuth.startsWith("Bearer ")) {
            String token = headerAuth.substring(7);
            logger.debug("Extracted JWT token: {}", token);
            return token;
        }

        logger.debug("No JWT token found in request");
        return null;
    }
}

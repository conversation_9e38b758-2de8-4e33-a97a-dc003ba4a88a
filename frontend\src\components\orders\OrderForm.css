.order-form-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.order-form-container h2 {
  margin-bottom: 20px;
}

.order-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-row .form-group {
  flex: 1;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input, .form-group select, .form-group textarea {
  width: 90%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.form-group input.error, .form-group select.error, .form-group textarea.error {
  border-color: #F44336;
}

.error-text {
  color: #F44336;
  font-size: 14px;
  margin-top: 5px;
}

.error-message {
  background-color: #FFEBEE;
  color: #F44336;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.order-items-section {
  margin-top: 30px;
  margin-bottom: 30px;
}

.order-items-section h3 {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.add-item-form {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.btn-add-item {
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  align-self: flex-end;
  margin-top: 24px;
}

.btn-add-item:hover {
  background-color: #0b7dda;
}

.table-responsive {
  overflow-x: auto;
}

.items-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.items-table th, .items-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}

.items-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.items-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.btn-remove-item {
  background-color: #F44336;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 10px;
  cursor: pointer;
}

.btn-remove-item:hover {
  background-color: #da190b;
}

.total-label {
  text-align: right;
  font-weight: bold;
}

.total-value {
  font-weight: bold;
  font-size: 18px;
}

.no-items {
  padding: 20px;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.btn-cancel, .btn-save {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.btn-cancel {
  background-color: #f2f2f2;
  color: #333;
}

.btn-save {
  background-color: #4CAF50;
  color: white;
}

.btn-cancel:hover {
  background-color: #e0e0e0;
}

.btn-save:hover {
  background-color: #45a049;
}

.btn-save:disabled {
  background-color: #a5d6a7;
  cursor: not-allowed;
}

.loading {
  text-align: center;
  padding: 20px;
}

.remark-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.remark-input:focus {
  outline: none;
  border-color: #2196F3;
  box-shadow: 0 0 3px rgba(33, 150, 243, 0.3);
}

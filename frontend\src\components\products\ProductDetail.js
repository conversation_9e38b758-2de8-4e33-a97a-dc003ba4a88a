import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import ProductService from '../../services/productService';
import './ProductDetail.css';

const ProductDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchProduct();
  }, [id]);

  const fetchProduct = async () => {
    try {
      setLoading(true);
      const data = await ProductService.getProductById(id);
      setProduct(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch product details. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        await ProductService.deleteProduct(id);
        navigate('/products');
      } catch (err) {
        setError('Failed to delete product. Please try again.');
        console.error(err);
      }
    }
  };

  if (loading) {
    return <div className="loading">Loading product details...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  if (!product) {
    return <div className="not-found">Product not found.</div>;
  }

  return (
    <div className="product-detail-container">
      <div className="product-detail-header">
        <h2>Menu Item Details</h2>
        <div className="product-actions">
          <Link to="/products" className="btn-back">Back to Menu</Link>
          <Link to={`/products/edit/${id}`} className="btn-edit">Edit</Link>
          <button onClick={handleDelete} className="btn-delete">Delete</button>
        </div>
      </div>

      <div className="product-detail-card">
        <div className="product-info">
          <h3>{product.name}</h3>

          <div className="info-row">
            <div className="info-label">ID:</div>
            <div className="info-value">{product.id}</div>
          </div>

          <div className="info-row">
            <div className="info-label">Alternative Name:</div>
            <div className="info-value">{product.altname || 'N/A'}</div>
          </div>

          <div className="info-row">
            <div className="info-label">Price:</div>
            <div className="info-value">RM {product.price.toFixed(2)}</div>
          </div>


          <div className="info-row">
            <div className="info-label">Category:</div>
            <div className="info-value">{product.category || 'N/A'}</div>
          </div>

          <div className="info-row">
            <div className="info-label">Status:</div>
            <div className="info-value">{product.isAvailable ? 'Available' : 'Not Available'}</div>
          </div>

          <div className="info-row">
            <div className="info-label">Created:</div>
            <div className="info-value">
              {new Date(product.createdAt).toLocaleString()}
            </div>
          </div>

          <div className="info-row">
            <div className="info-label">Last Updated:</div>
            <div className="info-value">
              {new Date(product.updatedAt).toLocaleString()}
            </div>
          </div>
        </div>

        <div className="product-description">
          <h4>Description</h4>
          <p>{product.description || 'No description available.'}</p>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
